using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.BuildingBlocks.Auth.Permissions;
using CoverGo.PoliciesV3.Application.Common.Interfaces;
using CoverGo.PoliciesV3.Application.Features.PolicyMembers.RegisterUpload;
using CoverGo.PoliciesV3.Application.Services;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.Policies.Exceptions;
using CoverGo.PoliciesV3.Domain.Endorsements;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Exceptions;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.Validators;
using CoverGo.PoliciesV3.Domain.CustomFields.Validation;
using Microsoft.Extensions.Logging;
using System.Security.Claims;
using LegacyPolicy = CoverGo.Policies.Client.Policy;

namespace CoverGo.PoliciesV3.Tests.Unit.Application.Features.PolicyMembers.RegisterUpload;

public class RegisterPolicyMemberUploadHandlerTests
{
    private readonly Mock<IRepository<PolicyMemberUpload, PolicyMemberUploadId>> _policyMemberUploadRepository;
    private readonly Mock<ILegacyPolicyService> _legacyPolicyService;
    private readonly Mock<IPolicyValidationService> _policyValidationService;
    private readonly Mock<IPolicyMemberFieldsSchemaProvider> _schemaProvider;
    private readonly Mock<IPermissionValidator> _permissionValidator;
    private readonly Mock<IFileSystemService> _fileSystemService;
    private readonly Mock<IFileParserFactory> _fileParserFactory;
    private readonly Mock<IFileParser> _fileParser;
    private readonly Mock<ILogger<RegisterPolicyMemberUploadHandler>> _logger;
    private readonly RegisterPolicyMemberUploadHandler _handler;
    private readonly IFixture _fixture;

    public RegisterPolicyMemberUploadHandlerTests()
    {
        _policyMemberUploadRepository = new Mock<IRepository<PolicyMemberUpload, PolicyMemberUploadId>>();
        _legacyPolicyService = new Mock<ILegacyPolicyService>();
        _policyValidationService = new Mock<IPolicyValidationService>();
        _schemaProvider = new Mock<IPolicyMemberFieldsSchemaProvider>();
        _permissionValidator = new Mock<IPermissionValidator>();
        _fileSystemService = new Mock<IFileSystemService>();
        _fileParserFactory = new Mock<IFileParserFactory>();
        _fileParser = new Mock<IFileParser>();
        _logger = new Mock<ILogger<RegisterPolicyMemberUploadHandler>>();

        _handler = new RegisterPolicyMemberUploadHandler(
            _policyMemberUploadRepository.Object,
            _legacyPolicyService.Object,
            _policyValidationService.Object,
            _schemaProvider.Object,
            _permissionValidator.Object,
            _fileSystemService.Object,
            _fileParserFactory.Object,
            _logger.Object);

        _fixture = new Fixture();
        _fixture.Customize<PolicyId>(c => c.FromFactory(() => PolicyId.New));
        _fixture.Customize<PolicyMemberUploadId>(c => c.FromFactory(() => PolicyMemberUploadId.New));
    }

    [Fact]
    public async Task Handle_WithValidRequest_ShouldReturnSuccessResponse()
    {
        // Arrange
        RegisterPolicyMemberUploadRequest request = CreateValidRequest();
        ClaimsIdentity identity = CreateClaimsIdentity();
        string tenantId = _fixture.Create<string>();
        Policy policy = CreateValidPolicy();
        byte[] fileContent = CreateValidCsvFileContent();
        string[] headers = new[] { "memberId", "planId", "effectiveDate" };
        int memberCount = 2;
        PolicyMemberFieldsSchema schema = CreateValidSchema();

        SetupSuccessfulMocks(policy, fileContent, headers, memberCount, schema);

        // Act
        RegisterPolicyMemberUploadResponse result = await _handler.Handle(request, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.PolicyId.Should().Be(request.PolicyId);
        result.EndorsementId.Should().Be(request.EndorsementId);
        result.Path.Should().Be(request.Path);
        result.MembersCount.Should().Be(memberCount);
        result.Status.Should().Be(PolicyMemberUploadStatus.REGISTERED.Value);
        result.PolicyMemberUploadId.Should().NotBeEmpty();
    }

    [Fact]
    public async Task Handle_WithUnauthorizedUser_ShouldThrowUnauthorizedException()
    {
        // Arrange
        RegisterPolicyMemberUploadRequest request = CreateValidRequest();

        _permissionValidator
            .Setup(x => x.AuthorizeAsync(request.Identity, It.IsAny<PermissionRequest>()))
            .ThrowsAsync(new UnauthorizedAccessException("Access denied"));

        // Act & Assert
        UnauthorizedAccessException exception = await Assert.ThrowsAsync<UnauthorizedAccessException>(
            () => _handler.Handle(request, CancellationToken.None));

        exception.Message.Should().Be("Access denied");
        _permissionValidator.Verify(x => x.AuthorizeAsync(request.Identity, It.IsAny<PermissionRequest>()), Times.Once);
    }

    [Fact]
    public async Task Handle_WithNonExistentPolicy_ShouldThrowPolicyNotFoundException()
    {
        // Arrange
        RegisterPolicyMemberUploadRequest request = CreateValidRequest();
        ClaimsIdentity identity = CreateClaimsIdentity();
        string tenantId = _fixture.Create<string>();

        _permissionValidator
            .Setup(x => x.AuthorizeAsync(It.IsAny<ClaimsIdentity>(), It.IsAny<PermissionRequest>()))
            .Returns(ValueTask.CompletedTask);

        _legacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((LegacyPolicy?)null);

        // Act & Assert
        PolicyNotFoundException exception = await Assert.ThrowsAsync<PolicyNotFoundException>(
            () => _handler.Handle(request, CancellationToken.None));

        exception.PolicyId.Should().Be(request.PolicyId.ToString());
        exception.Code.Should().Be("POLICY_NOT_FOUND");
    }

    [Fact]
    public async Task Handle_WithPolicyMissingId_ShouldThrowPolicyNotFoundException()
    {
        // Arrange
        RegisterPolicyMemberUploadRequest request = CreateValidRequest();
        ClaimsIdentity identity = CreateClaimsIdentity();
        string tenantId = _fixture.Create<string>();

        LegacyPolicy legacyPolicyWithoutId = CreateValidLegacyPolicy();
        legacyPolicyWithoutId.Id = null; // Missing essential data

        _permissionValidator
            .Setup(x => x.AuthorizeAsync(It.IsAny<ClaimsIdentity>(), It.IsAny<PermissionRequest>()))
            .Returns(ValueTask.CompletedTask);

        _legacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(legacyPolicyWithoutId);

        // Act & Assert
        PolicyNotFoundException exception = await Assert.ThrowsAsync<PolicyNotFoundException>(
            () => _handler.Handle(request, CancellationToken.None));

        exception.PolicyId.Should().Be(request.PolicyId.ToString());
        exception.Code.Should().Be("POLICY_NOT_FOUND");
    }

    [Fact]
    public async Task Handle_WithPolicyEmptyId_ShouldThrowPolicyNotFoundException()
    {
        // Arrange
        RegisterPolicyMemberUploadRequest request = CreateValidRequest();
        ClaimsIdentity identity = CreateClaimsIdentity();
        string tenantId = _fixture.Create<string>();

        LegacyPolicy legacyPolicyWithEmptyId = CreateValidLegacyPolicy();
        legacyPolicyWithEmptyId.Id = ""; // Empty essential data

        _permissionValidator
            .Setup(x => x.AuthorizeAsync(It.IsAny<ClaimsIdentity>(), It.IsAny<PermissionRequest>()))
            .Returns(ValueTask.CompletedTask);

        _legacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(legacyPolicyWithEmptyId);

        // Act & Assert
        PolicyNotFoundException exception = await Assert.ThrowsAsync<PolicyNotFoundException>(
            () => _handler.Handle(request, CancellationToken.None));

        exception.PolicyId.Should().Be(request.PolicyId.ToString());
        exception.Code.Should().Be("POLICY_NOT_FOUND");
    }

    [Fact]
    public async Task Handle_WithNullRequest_ShouldThrowNullReferenceException()
    {
        // Arrange
        ClaimsIdentity identity = CreateClaimsIdentity();
        string tenantId = _fixture.Create<string>();

        // Act & Assert
        // Handler doesn't have null checks, so it throws NullReferenceException when accessing request.PolicyId
        await Assert.ThrowsAsync<NullReferenceException>(
            () => _handler.Handle(null!, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_WithEmptyFilePath_ShouldThrowUploadFileNotFoundException()
    {
        // Arrange
        var request = new RegisterPolicyMemberUploadRequest
        {
            PolicyId = Guid.NewGuid(),
            EndorsementId = null,
            Path = "", // Empty path
            Identity = CreateClaimsIdentity(),
            TenantId = "test-tenant"
        };

        // Setup mocks to pass policy validation first
        _permissionValidator
            .Setup(x => x.AuthorizeAsync(It.IsAny<ClaimsIdentity>(), It.IsAny<PermissionRequest>()))
            .Returns(ValueTask.CompletedTask);

        LegacyPolicy legacyPolicy = CreateValidLegacyPolicy();
        _legacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(legacyPolicy);

        // File system will return null for empty path
        _fileSystemService
            .Setup(x => x.GetFileByPath("", It.IsAny<string?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((byte[]?)null);

        // Act & Assert
        // Handler validates policy first, then tries to get file, which throws UploadFileNotFoundException for empty path
        UploadFileNotFoundException exception = await Assert.ThrowsAsync<UploadFileNotFoundException>(
            () => _handler.Handle(request, CancellationToken.None));

        exception.PolicyId.Should().Be(request.PolicyId.ToString());
        exception.Path.Should().Be("");
    }

    [Fact]
    public async Task Handle_WithEmptyFileContent_ShouldThrowBadFileContentException()
    {
        // Arrange
        RegisterPolicyMemberUploadRequest request = CreateValidRequest();
        ClaimsIdentity identity = CreateClaimsIdentity();
        string tenantId = _fixture.Create<string>();
        byte[] emptyFileContent = Array.Empty<byte>();

        _permissionValidator
            .Setup(x => x.AuthorizeAsync(It.IsAny<ClaimsIdentity>(), It.IsAny<PermissionRequest>()))
            .Returns(ValueTask.CompletedTask);

        LegacyPolicy legacyPolicy = CreateValidLegacyPolicy();
        _legacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(legacyPolicy);

        _fileSystemService
            .Setup(x => x.GetFileByPath(It.IsAny<string>(), It.IsAny<string?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(emptyFileContent);

        // Act & Assert
        BadFileContentException exception = await Assert.ThrowsAsync<BadFileContentException>(
            () => _handler.Handle(request, CancellationToken.None));

        exception.ErrorCode.Should().Be(BadFileContentErrorCode.EMPTY_FILE);
    }

    [Fact]
    public async Task Handle_WithPolicyMissingProductId_ShouldThrowInvalidOperationException()
    {
        // Arrange
        RegisterPolicyMemberUploadRequest request = CreateValidRequest();
        ClaimsIdentity identity = CreateClaimsIdentity();
        string tenantId = _fixture.Create<string>();
        byte[] validFileContent = CreateValidCsvFileContent();

        _permissionValidator
            .Setup(x => x.AuthorizeAsync(It.IsAny<ClaimsIdentity>(), It.IsAny<PermissionRequest>()))
            .Returns(ValueTask.CompletedTask);

        LegacyPolicy legacyPolicyWithoutProductId = CreateValidLegacyPolicy();
        legacyPolicyWithoutProductId.ProductId = null; // Missing ProductId

        _legacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(legacyPolicyWithoutProductId);

        _fileSystemService
            .Setup(x => x.GetFileByPath(It.IsAny<string>(), It.IsAny<string?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(validFileContent);

        _fileParserFactory
            .Setup(x => x.CreateParser(validFileContent))
            .Returns(_fileParser.Object);

        var parseResult = new FileParseResult
        {
            Headers = ["memberId", "planId"],
            Contents = [new Dictionary<string, string?> { { "memberId", "MEM001" }, { "planId", "PLAN001" } }]
        };
        parseResult.InitializeHeadersSet();

        _fileParser
            .Setup(x => x.ParseFile(validFileContent))
            .Returns(parseResult);

        // Act & Assert
        InvalidOperationException exception = await Assert.ThrowsAsync<InvalidOperationException>(
            () => _handler.Handle(request, CancellationToken.None));

        exception.Message.Should().Contain("is missing ProductId");
    }

    [Fact]
    public async Task Handle_WithPolicyMissingProductPlan_ShouldThrowInvalidOperationException()
    {
        // Arrange
        RegisterPolicyMemberUploadRequest request = CreateValidRequest();
        ClaimsIdentity identity = CreateClaimsIdentity();
        string tenantId = _fixture.Create<string>();
        byte[] validFileContent = CreateValidCsvFileContent();

        _permissionValidator
            .Setup(x => x.AuthorizeAsync(It.IsAny<ClaimsIdentity>(), It.IsAny<PermissionRequest>()))
            .Returns(ValueTask.CompletedTask);

        LegacyPolicy legacyPolicyWithoutPlan = CreateValidLegacyPolicy();
        legacyPolicyWithoutPlan.ProductId!.Plan = null; // Missing Plan

        _legacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(legacyPolicyWithoutPlan);

        _fileSystemService
            .Setup(x => x.GetFileByPath(It.IsAny<string>(), It.IsAny<string?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(validFileContent);

        _fileParserFactory
            .Setup(x => x.CreateParser(validFileContent))
            .Returns(_fileParser.Object);

        var parseResult = new FileParseResult
        {
            Headers = ["memberId", "planId"],
            Contents = [new Dictionary<string, string?> { { "memberId", "MEM001" }, { "planId", "PLAN001" } }]
        };
        parseResult.InitializeHeadersSet();

        _fileParser
            .Setup(x => x.ParseFile(validFileContent))
            .Returns(parseResult);

        // Act & Assert
        InvalidOperationException exception = await Assert.ThrowsAsync<InvalidOperationException>(
            () => _handler.Handle(request, CancellationToken.None));

        exception.Message.Should().Contain("is missing Plan");
    }

    [Fact]
    public async Task Handle_WithPolicyMissingProductType_ShouldThrowInvalidOperationException()
    {
        // Arrange
        RegisterPolicyMemberUploadRequest request = CreateValidRequest();
        ClaimsIdentity identity = CreateClaimsIdentity();
        string tenantId = _fixture.Create<string>();
        byte[] validFileContent = CreateValidCsvFileContent();

        _permissionValidator
            .Setup(x => x.AuthorizeAsync(It.IsAny<ClaimsIdentity>(), It.IsAny<PermissionRequest>()))
            .Returns(ValueTask.CompletedTask);

        LegacyPolicy legacyPolicyWithoutType = CreateValidLegacyPolicy();
        legacyPolicyWithoutType.ProductId!.Type = null; // Missing Type

        _legacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(legacyPolicyWithoutType);

        _fileSystemService
            .Setup(x => x.GetFileByPath(It.IsAny<string>(), It.IsAny<string?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(validFileContent);

        _fileParserFactory
            .Setup(x => x.CreateParser(validFileContent))
            .Returns(_fileParser.Object);

        var parseResult = new FileParseResult
        {
            Headers = ["memberId", "planId"],
            Contents = [new Dictionary<string, string?> { { "memberId", "MEM001" }, { "planId", "PLAN001" } }]
        };
        parseResult.InitializeHeadersSet();

        _fileParser
            .Setup(x => x.ParseFile(validFileContent))
            .Returns(parseResult);

        // Act & Assert
        InvalidOperationException exception = await Assert.ThrowsAsync<InvalidOperationException>(
            () => _handler.Handle(request, CancellationToken.None));

        exception.Message.Should().Contain("is missing Type");
    }

    [Fact]
    public async Task Handle_WithFileNotFound_ShouldThrowUploadFileNotFoundException()
    {
        // Arrange
        RegisterPolicyMemberUploadRequest request = CreateValidRequest();
        ClaimsIdentity identity = CreateClaimsIdentity();
        string tenantId = _fixture.Create<string>();
        Policy policy = CreateValidPolicy();

        _permissionValidator
            .Setup(x => x.AuthorizeAsync(It.IsAny<ClaimsIdentity>(), It.IsAny<PermissionRequest>()))
            .Returns(ValueTask.CompletedTask);

        LegacyPolicy legacyPolicy = CreateValidLegacyPolicy();
        _legacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(legacyPolicy);

        _fileSystemService
            .Setup(x => x.GetFileByPath(It.IsAny<string>(), It.IsAny<string?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((byte[]?)null);

        // Act & Assert
        UploadFileNotFoundException exception = await Assert.ThrowsAsync<UploadFileNotFoundException>(
            () => _handler.Handle(request, CancellationToken.None));

        exception.PolicyId.Should().Be(request.PolicyId.ToString());
        exception.Path.Should().Be(request.Path);
        exception.Code.Should().Be("UPLOAD_FILE_NOT_FOUND");
    }

    [Fact]
    public async Task Handle_WithEmptyFile_ShouldThrowBadFileContentException()
    {
        // Arrange
        RegisterPolicyMemberUploadRequest request = CreateValidRequest();
        ClaimsIdentity identity = CreateClaimsIdentity();
        string tenantId = _fixture.Create<string>();
        Policy policy = CreateValidPolicy();
        byte[] emptyFileContent = Array.Empty<byte>();

        _permissionValidator
            .Setup(x => x.AuthorizeAsync(It.IsAny<ClaimsIdentity>(), It.IsAny<PermissionRequest>()))
            .Returns(ValueTask.CompletedTask);

        LegacyPolicy legacyPolicy = CreateValidLegacyPolicy();
        _legacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(legacyPolicy);

        _fileSystemService
            .Setup(x => x.GetFileByPath(It.IsAny<string>(), It.IsAny<string?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(emptyFileContent);

        _fileParserFactory
            .Setup(x => x.CreateParser(emptyFileContent))
            .Throws(new BadFileContentException(BadFileContentErrorCode.EMPTY_FILE, "File content is empty"));

        // Act & Assert
        BadFileContentException exception = await Assert.ThrowsAsync<BadFileContentException>(
            () => _handler.Handle(request, CancellationToken.None));

        exception.ErrorCode.Should().Be(BadFileContentErrorCode.EMPTY_FILE);
        exception.Code.Should().Be(BadFileContentErrorCode.EMPTY_FILE);
    }

    [Fact]
    public async Task Handle_WithNullParseResult_ShouldThrowBadFileContentException()
    {
        // Arrange
        RegisterPolicyMemberUploadRequest request = CreateValidRequest();
        byte[] validFileContent = CreateValidCsvFileContent();

        _permissionValidator
            .Setup(x => x.AuthorizeAsync(It.IsAny<ClaimsIdentity>(), It.IsAny<PermissionRequest>()))
            .Returns(ValueTask.CompletedTask);

        LegacyPolicy legacyPolicy = CreateValidLegacyPolicy();
        _legacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(legacyPolicy);

        _fileSystemService
            .Setup(x => x.GetFileByPath(It.IsAny<string>(), It.IsAny<string?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(validFileContent);

        _fileParserFactory
            .Setup(x => x.CreateParser(validFileContent))
            .Returns(_fileParser.Object);

        _fileParser
            .Setup(x => x.ParseFile(validFileContent))
            .Returns((FileParseResult?)null); // Return null to trigger exception

        // Act & Assert
        BadFileContentException exception = await Assert.ThrowsAsync<BadFileContentException>(
            () => _handler.Handle(request, CancellationToken.None));

        exception.ErrorCode.Should().Be(BadFileContentErrorCode.INVALID_ROW);
        exception.Message.Should().Be("Failed to parse file content");
    }

    [Fact]
    public async Task Handle_WithNullHeaders_ShouldThrowBadFileContentException()
    {
        // Arrange
        RegisterPolicyMemberUploadRequest request = CreateValidRequest();
        byte[] validFileContent = CreateValidCsvFileContent();

        _permissionValidator
            .Setup(x => x.AuthorizeAsync(It.IsAny<ClaimsIdentity>(), It.IsAny<PermissionRequest>()))
            .Returns(ValueTask.CompletedTask);

        LegacyPolicy legacyPolicy = CreateValidLegacyPolicy();
        _legacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(legacyPolicy);

        _fileSystemService
            .Setup(x => x.GetFileByPath(It.IsAny<string>(), It.IsAny<string?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(validFileContent);

        _fileParserFactory
            .Setup(x => x.CreateParser(validFileContent))
            .Returns(_fileParser.Object);

        var parseResult = new FileParseResult
        {
            Headers = null!, // Null headers to trigger exception
            Contents = [new Dictionary<string, string?> { { "memberId", "MEM001" } }]
        };

        _fileParser
            .Setup(x => x.ParseFile(validFileContent))
            .Returns(parseResult);

        // Act & Assert
        BadFileContentException exception = await Assert.ThrowsAsync<BadFileContentException>(
            () => _handler.Handle(request, CancellationToken.None));

        exception.ErrorCode.Should().Be(BadFileContentErrorCode.NO_COLUMN);
        exception.Message.Should().Be("No headers found in file");
    }

    [Fact]
    public async Task Handle_WithEmptyHeaders_ShouldThrowBadFileContentException()
    {
        // Arrange
        RegisterPolicyMemberUploadRequest request = CreateValidRequest();
        byte[] validFileContent = CreateValidCsvFileContent();

        _permissionValidator
            .Setup(x => x.AuthorizeAsync(It.IsAny<ClaimsIdentity>(), It.IsAny<PermissionRequest>()))
            .Returns(ValueTask.CompletedTask);

        LegacyPolicy legacyPolicy = CreateValidLegacyPolicy();
        _legacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(legacyPolicy);

        _fileSystemService
            .Setup(x => x.GetFileByPath(It.IsAny<string>(), It.IsAny<string?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(validFileContent);

        _fileParserFactory
            .Setup(x => x.CreateParser(validFileContent))
            .Returns(_fileParser.Object);

        var parseResult = new FileParseResult
        {
            Headers = [], // Empty headers to trigger exception
            Contents = [new Dictionary<string, string?> { { "memberId", "MEM001" } }]
        };

        _fileParser
            .Setup(x => x.ParseFile(validFileContent))
            .Returns(parseResult);

        // Act & Assert
        BadFileContentException exception = await Assert.ThrowsAsync<BadFileContentException>(
            () => _handler.Handle(request, CancellationToken.None));

        exception.ErrorCode.Should().Be(BadFileContentErrorCode.NO_COLUMN);
        exception.Message.Should().Be("No headers found in file");
    }

    [Fact]
    public async Task Handle_WithZeroMemberCount_ShouldThrowBadFileContentException()
    {
        // Arrange
        RegisterPolicyMemberUploadRequest request = CreateValidRequest();
        byte[] validFileContent = CreateValidCsvFileContent();

        _permissionValidator
            .Setup(x => x.AuthorizeAsync(It.IsAny<ClaimsIdentity>(), It.IsAny<PermissionRequest>()))
            .Returns(ValueTask.CompletedTask);

        LegacyPolicy legacyPolicy = CreateValidLegacyPolicy();
        _legacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(legacyPolicy);

        _fileSystemService
            .Setup(x => x.GetFileByPath(It.IsAny<string>(), It.IsAny<string?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(validFileContent);

        _fileParserFactory
            .Setup(x => x.CreateParser(validFileContent))
            .Returns(_fileParser.Object);

        var parseResult = new FileParseResult
        {
            Headers = ["memberId", "planId"],
            Contents = [] // Empty contents to trigger zero count
        };

        _fileParser
            .Setup(x => x.ParseFile(validFileContent))
            .Returns(parseResult);

        // Act & Assert
        BadFileContentException exception = await Assert.ThrowsAsync<BadFileContentException>(
            () => _handler.Handle(request, CancellationToken.None));

        exception.ErrorCode.Should().Be(BadFileContentErrorCode.NO_MEMBER);
        exception.Message.Should().Be("No member data found in file");
    }

    [Fact]
    public async Task Handle_WithPolicyMissingProductPlanWhitespace_ShouldThrowInvalidOperationException()
    {
        // Arrange
        RegisterPolicyMemberUploadRequest request = CreateValidRequest();
        byte[] validFileContent = CreateValidCsvFileContent();

        _permissionValidator
            .Setup(x => x.AuthorizeAsync(It.IsAny<ClaimsIdentity>(), It.IsAny<PermissionRequest>()))
            .Returns(ValueTask.CompletedTask);

        LegacyPolicy legacyPolicyWithWhitespacePlan = CreateValidLegacyPolicy();
        legacyPolicyWithWhitespacePlan.ProductId!.Plan = "   "; // Whitespace Plan

        _legacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(legacyPolicyWithWhitespacePlan);

        _fileSystemService
            .Setup(x => x.GetFileByPath(It.IsAny<string>(), It.IsAny<string?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(validFileContent);

        _fileParserFactory
            .Setup(x => x.CreateParser(validFileContent))
            .Returns(_fileParser.Object);

        var parseResult = new FileParseResult
        {
            Headers = ["memberId", "planId"],
            Contents = [new Dictionary<string, string?> { { "memberId", "MEM001" }, { "planId", "PLAN001" } }]
        };
        parseResult.InitializeHeadersSet();

        _fileParser
            .Setup(x => x.ParseFile(validFileContent))
            .Returns(parseResult);

        // Act & Assert
        InvalidOperationException exception = await Assert.ThrowsAsync<InvalidOperationException>(
            () => _handler.Handle(request, CancellationToken.None));

        exception.Message.Should().Contain("is missing Plan");
    }

    [Fact]
    public async Task Handle_WithPolicyMissingProductTypeWhitespace_ShouldThrowInvalidOperationException()
    {
        // Arrange
        RegisterPolicyMemberUploadRequest request = CreateValidRequest();
        byte[] validFileContent = CreateValidCsvFileContent();

        _permissionValidator
            .Setup(x => x.AuthorizeAsync(It.IsAny<ClaimsIdentity>(), It.IsAny<PermissionRequest>()))
            .Returns(ValueTask.CompletedTask);

        LegacyPolicy legacyPolicyWithWhitespaceType = CreateValidLegacyPolicy();
        legacyPolicyWithWhitespaceType.ProductId!.Type = "   "; // Whitespace Type

        _legacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(legacyPolicyWithWhitespaceType);

        _fileSystemService
            .Setup(x => x.GetFileByPath(It.IsAny<string>(), It.IsAny<string?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(validFileContent);

        _fileParserFactory
            .Setup(x => x.CreateParser(validFileContent))
            .Returns(_fileParser.Object);

        var parseResult = new FileParseResult
        {
            Headers = ["memberId", "planId"],
            Contents = [new Dictionary<string, string?> { { "memberId", "MEM001" }, { "planId", "PLAN001" } }]
        };
        parseResult.InitializeHeadersSet();

        _fileParser
            .Setup(x => x.ParseFile(validFileContent))
            .Returns(parseResult);

        // Act & Assert
        InvalidOperationException exception = await Assert.ThrowsAsync<InvalidOperationException>(
            () => _handler.Handle(request, CancellationToken.None));

        exception.Message.Should().Contain("is missing Type");
    }

    [Fact]
    public async Task Handle_WithNullSchema_ShouldThrowInvalidOperationException()
    {
        // Arrange
        RegisterPolicyMemberUploadRequest request = CreateValidRequest();
        byte[] validFileContent = CreateValidCsvFileContent();

        _permissionValidator
            .Setup(x => x.AuthorizeAsync(It.IsAny<ClaimsIdentity>(), It.IsAny<PermissionRequest>()))
            .Returns(ValueTask.CompletedTask);

        LegacyPolicy legacyPolicy = CreateValidLegacyPolicy();
        _legacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(legacyPolicy);

        _fileSystemService
            .Setup(x => x.GetFileByPath(It.IsAny<string>(), It.IsAny<string?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(validFileContent);

        _fileParserFactory
            .Setup(x => x.CreateParser(validFileContent))
            .Returns(_fileParser.Object);

        var parseResult = new FileParseResult
        {
            Headers = ["memberId", "planId"],
            Contents = [new Dictionary<string, string?> { { "memberId", "MEM001" }, { "planId", "PLAN001" } }]
        };
        parseResult.InitializeHeadersSet();

        _fileParser
            .Setup(x => x.ParseFile(validFileContent))
            .Returns(parseResult);

        _schemaProvider
            .Setup(x => x.GetMemberUploadSchema(It.IsAny<string?>(), It.IsAny<ProductId>(), It.IsAny<EndorsementId?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((PolicyMemberFieldsSchema?)null); // Return null schema

        // Act & Assert
        InvalidOperationException exception = await Assert.ThrowsAsync<InvalidOperationException>(
            () => _handler.Handle(request, CancellationToken.None));

        exception.Message.Should().Be("Failed to retrieve valid schema for policy member upload validation");
    }



    [Fact]
    public async Task Handle_WithMissingOneOfMandatoryColumns_ShouldThrowBadFileContentException()
    {
        // Arrange
        RegisterPolicyMemberUploadRequest request = CreateValidRequest();
        byte[] validFileContent = CreateValidCsvFileContent();

        _permissionValidator
            .Setup(x => x.AuthorizeAsync(It.IsAny<ClaimsIdentity>(), It.IsAny<PermissionRequest>()))
            .Returns(ValueTask.CompletedTask);

        LegacyPolicy legacyPolicy = CreateValidLegacyPolicy();
        _legacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(legacyPolicy);

        _fileSystemService
            .Setup(x => x.GetFileByPath(It.IsAny<string>(), It.IsAny<string?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(validFileContent);

        _fileParserFactory
            .Setup(x => x.CreateParser(validFileContent))
            .Returns(_fileParser.Object);

        var parseResult = new FileParseResult
        {
            Headers = ["memberId", "planId"], // Missing both required fields from OneOf validation
            Contents = [new Dictionary<string, string?> { { "memberId", "MEM001" }, { "planId", "PLAN001" } }]
        };
        parseResult.InitializeHeadersSet();

        _fileParser
            .Setup(x => x.ParseFile(validFileContent))
            .Returns(parseResult);

        var schema = CreateSchemaWithOneOfValidation();
        _schemaProvider
            .Setup(x => x.GetMemberUploadSchema(It.IsAny<string?>(), It.IsAny<ProductId>(), It.IsAny<EndorsementId?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(schema);

        // Act & Assert
        BadFileContentException exception = await Assert.ThrowsAsync<BadFileContentException>(
            () => _handler.Handle(request, CancellationToken.None));

        exception.ErrorCode.Should().Be(BadFileContentErrorCode.MISSING_ONE_OF_MANDATORY_COLUMNS);
        exception.Message.Should().Contain("Missing one of mandatory columns");
    }

    [Fact]
    public async Task Handle_WithMissingMandatoryColumns_ShouldThrowBadFileContentException()
    {
        // Arrange
        RegisterPolicyMemberUploadRequest request = CreateValidRequest();
        byte[] validFileContent = CreateValidCsvFileContent();

        _permissionValidator
            .Setup(x => x.AuthorizeAsync(It.IsAny<ClaimsIdentity>(), It.IsAny<PermissionRequest>()))
            .Returns(ValueTask.CompletedTask);

        LegacyPolicy legacyPolicy = CreateValidLegacyPolicy();
        _legacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(legacyPolicy);

        _fileSystemService
            .Setup(x => x.GetFileByPath(It.IsAny<string>(), It.IsAny<string?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(validFileContent);

        _fileParserFactory
            .Setup(x => x.CreateParser(validFileContent))
            .Returns(_fileParser.Object);

        var parseResult = new FileParseResult
        {
            Headers = ["memberId"], // Missing required field "firstName"
            Contents = [new Dictionary<string, string?> { { "memberId", "MEM001" } }]
        };
        parseResult.InitializeHeadersSet();

        _fileParser
            .Setup(x => x.ParseFile(validFileContent))
            .Returns(parseResult);

        var schema = CreateSchemaWithMandatoryFields();
        _schemaProvider
            .Setup(x => x.GetMemberUploadSchema(It.IsAny<string?>(), It.IsAny<ProductId>(), It.IsAny<EndorsementId?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(schema);

        // Act & Assert
        BadFileContentException exception = await Assert.ThrowsAsync<BadFileContentException>(
            () => _handler.Handle(request, CancellationToken.None));

        exception.ErrorCode.Should().Be(BadFileContentErrorCode.MISSING_MANDATORY_COLUMNS);
        exception.Message.Should().Contain("Mandatory column(s) are missing");
        exception.Message.Should().Contain("First Name"); // Uses Label, not Name
    }

    [Fact]
    public async Task Handle_WithExtraFormulaColumns_ShouldThrowBadFileContentException()
    {
        // Arrange
        RegisterPolicyMemberUploadRequest request = CreateValidRequest();
        byte[] validFileContent = CreateValidCsvFileContent();

        _permissionValidator
            .Setup(x => x.AuthorizeAsync(It.IsAny<ClaimsIdentity>(), It.IsAny<PermissionRequest>()))
            .Returns(ValueTask.CompletedTask);

        LegacyPolicy legacyPolicy = CreateValidLegacyPolicy();
        _legacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(legacyPolicy);

        _fileSystemService
            .Setup(x => x.GetFileByPath(It.IsAny<string>(), It.IsAny<string?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(validFileContent);

        _fileParserFactory
            .Setup(x => x.CreateParser(validFileContent))
            .Returns(_fileParser.Object);

        var parseResult = new FileParseResult
        {
            Headers = ["Calculated Field"], // Contains formula field that should not be uploaded (using Label)
            Contents = [new Dictionary<string, string?> { { "Calculated Field", "100" } }]
        };
        parseResult.InitializeHeadersSet();

        _fileParser
            .Setup(x => x.ParseFile(validFileContent))
            .Returns(parseResult);

        var schema = CreateSchemaWithFormulaFields();
        _schemaProvider
            .Setup(x => x.GetMemberUploadSchema(It.IsAny<string?>(), It.IsAny<ProductId>(), It.IsAny<EndorsementId?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(schema);

        // Act & Assert
        BadFileContentException exception = await Assert.ThrowsAsync<BadFileContentException>(
            () => _handler.Handle(request, CancellationToken.None));

        exception.ErrorCode.Should().Be(BadFileContentErrorCode.EXTRA_COLUMN);
        exception.Message.Should().Contain("Please remove column(s): Calculated Field"); // Uses Label, not Name
    }

    [Fact]
    public async Task Handle_WithMissingMandatoryObjectSubfields_ShouldThrowBadFileContentException()
    {
        // Arrange
        RegisterPolicyMemberUploadRequest request = CreateValidRequest();
        byte[] validFileContent = CreateValidCsvFileContent();

        _permissionValidator
            .Setup(x => x.AuthorizeAsync(It.IsAny<ClaimsIdentity>(), It.IsAny<PermissionRequest>()))
            .Returns(ValueTask.CompletedTask);

        LegacyPolicy legacyPolicy = CreateValidLegacyPolicy();
        _legacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(legacyPolicy);

        _fileSystemService
            .Setup(x => x.GetFileByPath(It.IsAny<string>(), It.IsAny<string?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(validFileContent);

        _fileParserFactory
            .Setup(x => x.CreateParser(validFileContent))
            .Returns(_fileParser.Object);

        var parseResult = new FileParseResult
        {
            Headers = ["memberId"], // Missing required object subfield
            Contents = [new Dictionary<string, string?> { { "memberId", "MEM001" } }]
        };
        parseResult.InitializeHeadersSet();

        _fileParser
            .Setup(x => x.ParseFile(validFileContent))
            .Returns(parseResult);

        var schema = CreateSchemaWithObjectFields();
        _schemaProvider
            .Setup(x => x.GetMemberUploadSchema(It.IsAny<string?>(), It.IsAny<ProductId>(), It.IsAny<EndorsementId?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(schema);

        // Act & Assert
        BadFileContentException exception = await Assert.ThrowsAsync<BadFileContentException>(
            () => _handler.Handle(request, CancellationToken.None));

        exception.ErrorCode.Should().Be(BadFileContentErrorCode.MISSING_MANDATORY_COLUMNS);
        exception.Message.Should().Contain("Mandatory column(s) are missing");
    }

    [Fact]
    public async Task Handle_WithValidSchemaButNoValidationErrors_ShouldSucceed()
    {
        // Arrange
        RegisterPolicyMemberUploadRequest request = CreateValidRequest();
        byte[] validFileContent = CreateValidCsvFileContent();

        _permissionValidator
            .Setup(x => x.AuthorizeAsync(It.IsAny<ClaimsIdentity>(), It.IsAny<PermissionRequest>()))
            .Returns(ValueTask.CompletedTask);

        LegacyPolicy legacyPolicy = CreateValidLegacyPolicy();
        _legacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(legacyPolicy);

        _fileSystemService
            .Setup(x => x.GetFileByPath(It.IsAny<string>(), It.IsAny<string?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(validFileContent);

        _fileParserFactory
            .Setup(x => x.CreateParser(validFileContent))
            .Returns(_fileParser.Object);

        var parseResult = new FileParseResult
        {
            Headers = ["memberId"], // Contains all required fields (no mandatory fields in this schema)
            Contents = [new Dictionary<string, string?> { { "memberId", "MEM001" } }]
        };
        parseResult.InitializeHeadersSet();

        _fileParser
            .Setup(x => x.ParseFile(validFileContent))
            .Returns(parseResult);

        var schema = CreateValidSchema(); // Use schema with no mandatory fields
        _schemaProvider
            .Setup(x => x.GetMemberUploadSchema(It.IsAny<string?>(), It.IsAny<ProductId>(), It.IsAny<EndorsementId?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(schema);

        _policyMemberUploadRepository
            .Setup(x => x.InsertAsync(It.IsAny<PolicyMemberUpload>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(It.IsAny<PolicyMemberUpload>());

        // Act
        RegisterPolicyMemberUploadResponse result = await _handler.Handle(request, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.PolicyId.Should().Be(request.PolicyId);
        result.MembersCount.Should().Be(1);
    }

    [Fact]
    public async Task Handle_WithNullOneOfValidations_ShouldNotThrowException()
    {
        // Arrange
        RegisterPolicyMemberUploadRequest request = CreateValidRequest();
        byte[] validFileContent = CreateValidCsvFileContent();

        _permissionValidator
            .Setup(x => x.AuthorizeAsync(It.IsAny<ClaimsIdentity>(), It.IsAny<PermissionRequest>()))
            .Returns(ValueTask.CompletedTask);

        LegacyPolicy legacyPolicy = CreateValidLegacyPolicy();
        _legacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(legacyPolicy);

        _fileSystemService
            .Setup(x => x.GetFileByPath(It.IsAny<string>(), It.IsAny<string?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(validFileContent);

        _fileParserFactory
            .Setup(x => x.CreateParser(validFileContent))
            .Returns(_fileParser.Object);

        var parseResult = new FileParseResult
        {
            Headers = ["memberId"],
            Contents = [new Dictionary<string, string?> { { "memberId", "MEM001" } }]
        };
        parseResult.InitializeHeadersSet();

        _fileParser
            .Setup(x => x.ParseFile(validFileContent))
            .Returns(parseResult);

        var schema = new PolicyMemberFieldsSchema
        {
            MemberFields = [new PolicyMemberFieldDefinition
            {
                Name = "memberId",
                Label = "Member ID",
                Type = new StringFieldType(),
                IsRequired = false,
                IsUnique = false
            }],
            OneOfValidations = null // Null OneOfValidations should not cause issues
        };

        _schemaProvider
            .Setup(x => x.GetMemberUploadSchema(It.IsAny<string?>(), It.IsAny<ProductId>(), It.IsAny<EndorsementId?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(schema);

        _policyMemberUploadRepository
            .Setup(x => x.InsertAsync(It.IsAny<PolicyMemberUpload>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(It.IsAny<PolicyMemberUpload>());

        // Act
        RegisterPolicyMemberUploadResponse result = await _handler.Handle(request, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.PolicyId.Should().Be(request.PolicyId);
    }

    [Fact]
    public async Task Handle_WithMultipleValidationErrors_ShouldThrowBadFileContentExceptionWithMultipleErrors()
    {
        // Arrange
        RegisterPolicyMemberUploadRequest request = CreateValidRequest();
        byte[] validFileContent = CreateValidCsvFileContent();

        _permissionValidator
            .Setup(x => x.AuthorizeAsync(It.IsAny<ClaimsIdentity>(), It.IsAny<PermissionRequest>()))
            .Returns(ValueTask.CompletedTask);

        LegacyPolicy legacyPolicy = CreateValidLegacyPolicy();
        _legacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(legacyPolicy);

        _fileSystemService
            .Setup(x => x.GetFileByPath(It.IsAny<string>(), It.IsAny<string?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(validFileContent);

        _fileParserFactory
            .Setup(x => x.CreateParser(validFileContent))
            .Returns(_fileParser.Object);

        var parseResult = new FileParseResult
        {
            Headers = ["Calculated Field"], // Contains formula field (should be removed) and missing mandatory field "firstName"
            Contents = [new Dictionary<string, string?> { { "Calculated Field", "100" } }]
        };
        parseResult.InitializeHeadersSet();

        _fileParser
            .Setup(x => x.ParseFile(validFileContent))
            .Returns(parseResult);

        var schema = CreateSchemaWithMultipleValidationIssues();
        _schemaProvider
            .Setup(x => x.GetMemberUploadSchema(It.IsAny<string?>(), It.IsAny<ProductId>(), It.IsAny<EndorsementId?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(schema);

        // Act & Assert
        BadFileContentException exception = await Assert.ThrowsAsync<BadFileContentException>(
            () => _handler.Handle(request, CancellationToken.None));

        // Should contain multiple error messages
        exception.Message.Should().Contain("Mandatory column(s) are missing");
        exception.Message.Should().Contain("Please remove column(s): Calculated Field"); // Uses Label, not Name
    }

    private static RegisterPolicyMemberUploadRequest CreateValidRequest() => new()
    {
        PolicyId = Guid.NewGuid(),
        EndorsementId = null,
        Path = "uploads/test-file.csv",
        Identity = CreateClaimsIdentity(),
        TenantId = "test-tenant"
    };

    private static ClaimsIdentity CreateClaimsIdentity() => new(new[]
        {
            new Claim(ClaimTypes.NameIdentifier, Guid.NewGuid().ToString()),
            new Claim(ClaimTypes.Name, "test-user")
        });

    private static Policy CreateValidPolicy()
    {
        var policy = Policy.Create(
            "POL-001",
            DateOnly.FromDateTime(DateTime.Today),
            DateOnly.FromDateTime(DateTime.Today.AddYears(1)));

        return policy;
    }

    private static LegacyPolicy CreateValidLegacyPolicy() => new()
    {
        Id = Guid.NewGuid().ToString(),
        IssuerNumber = "POL-001",
        StartDate = DateTime.Today,
        EndDate = DateTime.Today.AddYears(1),
        IsIssued = false,
        ContractHolder = new CoverGo.Policies.Client.Entity
        {
            Id = Guid.NewGuid().ToString()
        },
        ProductId = new CoverGo.Policies.Client.ProductId
        {
            Plan = "TEST_PLAN",
            Version = "1.0",
            Type = "INDIVIDUAL"
        }
    };

    private static byte[] CreateValidCsvFileContent()
    {
        string csvContent = "memberId,planId,effectiveDate\nMEM001,PLAN001,2024-01-01\nMEM002,PLAN001,2024-01-01";
        return System.Text.Encoding.UTF8.GetBytes(csvContent);
    }

    private static PolicyMemberFieldsSchema CreateValidSchema()
    {
        // Create a simple schema that will work with the current implementation
        // We need to provide the required MemberFields property as IReadOnlyList
        var memberFields = new List<PolicyMemberFieldDefinition>();
        return new PolicyMemberFieldsSchema
        {
            MemberFields = memberFields
        };
    }

    private static PolicyMemberFieldsSchema CreateSchemaWithOneOfValidation()
    {
        var field1 = new PolicyMemberFieldDefinition
        {
            Name = "email",
            Label = "Email",
            Type = new StringFieldType(),
            IsRequired = true,
            IsUnique = false
        };

        var field2 = new PolicyMemberFieldDefinition
        {
            Name = "phone",
            Label = "Phone",
            Type = new StringFieldType(),
            IsRequired = true,
            IsUnique = false
        };

        var oneOfValidation = new CustomFieldOneOfValidation
        {
            Validations = [
                new CustomFieldRequiredValidation { Field = field1, IsRequired = true },
                new CustomFieldRequiredValidation { Field = field2, IsRequired = true }
            ]
        };

        return new PolicyMemberFieldsSchema
        {
            MemberFields = [field1, field2],
            OneOfValidations = [oneOfValidation]
        };
    }

    private static PolicyMemberFieldsSchema CreateSchemaWithMandatoryFields()
    {
        var mandatoryField = new PolicyMemberFieldDefinition
        {
            Name = "firstName",
            Label = "First Name",
            Type = new StringFieldType(),
            IsRequired = true,
            IsUnique = false
        };

        var optionalField = new PolicyMemberFieldDefinition
        {
            Name = "memberId",
            Label = "Member ID",
            Type = new StringFieldType(),
            IsRequired = false,
            IsUnique = false
        };

        return new PolicyMemberFieldsSchema
        {
            MemberFields = [mandatoryField, optionalField]
        };
    }

    private static PolicyMemberFieldsSchema CreateSchemaWithFormulaFields()
    {
        var formulaField = new PolicyMemberFieldDefinition
        {
            Name = "calculatedField",
            Label = "Calculated Field",
            Type = new FormulaFieldType(),
            IsRequired = false,
            IsUnique = false
        };

        return new PolicyMemberFieldsSchema
        {
            MemberFields = [formulaField]
        };
    }

    private static PolicyMemberFieldsSchema CreateSchemaWithObjectFields()
    {
        var requiredSubfield = new PolicyMemberFieldDefinition
        {
            Name = "address.street",
            Label = "Street Address",
            Type = new StringFieldType(),
            IsRequired = true,
            IsUnique = false
        };

        var objectField = new PolicyMemberFieldDefinition
        {
            Name = "address",
            Label = "Address",
            Type = new ObjectFieldType([requiredSubfield]),
            IsRequired = true,
            IsUnique = false
        };

        return new PolicyMemberFieldsSchema
        {
            MemberFields = [objectField]
        };
    }

    private static PolicyMemberFieldsSchema CreateSchemaWithMultipleValidationIssues()
    {
        var mandatoryField = new PolicyMemberFieldDefinition
        {
            Name = "firstName",
            Label = "First Name",
            Type = new StringFieldType(),
            IsRequired = true,
            IsUnique = false
        };

        var formulaField = new PolicyMemberFieldDefinition
        {
            Name = "calculatedField",
            Label = "Calculated Field",
            Type = new FormulaFieldType(),
            IsRequired = false,
            IsUnique = false
        };

        return new PolicyMemberFieldsSchema
        {
            MemberFields = [mandatoryField, formulaField]
        };
    }

    private void SetupSuccessfulMocks(Policy policy, byte[] fileContent, string[] headers, int memberCount, PolicyMemberFieldsSchema schema)
    {
        _permissionValidator
            .Setup(x => x.AuthorizeAsync(It.IsAny<ClaimsIdentity>(), It.IsAny<PermissionRequest>()))
            .Returns(ValueTask.CompletedTask);

        LegacyPolicy legacyPolicy = CreateValidLegacyPolicy();
        _legacyPolicyService
            .Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(legacyPolicy);

        _fileSystemService
            .Setup(x => x.GetFileByPath(It.IsAny<string>(), It.IsAny<string?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(fileContent);

        _fileParserFactory
            .Setup(x => x.CreateParser(fileContent))
            .Returns(_fileParser.Object);

        var memberData = Enumerable.Range(0, memberCount)
            .Select(_ => new Dictionary<string, string?> { { "memberId", "MEM001" }, { "planId", "PLAN001" } } as IReadOnlyDictionary<string, string?>)
            .ToList();

        var parseResult = new FileParseResult
        {
            Headers = [.. headers],
            Contents = memberData
        };
        parseResult.InitializeHeadersSet();

        _fileParser
            .Setup(x => x.ParseFile(fileContent))
            .Returns(parseResult);

        // Keep backward compatibility for existing GetHeaders/GetContents calls
        _fileParser
            .Setup(x => x.GetHeaders(fileContent))
            .Returns(headers);

        _fileParser
            .Setup(x => x.GetContents(fileContent))
            .Returns([.. memberData.Select(dict => (IDictionary<string, string?>)dict.ToDictionary(kvp => kvp.Key, kvp => kvp.Value))]);

        _schemaProvider
            .Setup(x => x.GetMemberUploadSchema(It.IsAny<string?>(), It.IsAny<ProductId>(), It.IsAny<EndorsementId?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(schema);

        _policyMemberUploadRepository
            .Setup(x => x.InsertAsync(It.IsAny<PolicyMemberUpload>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(It.IsAny<PolicyMemberUpload>());
    }
}
