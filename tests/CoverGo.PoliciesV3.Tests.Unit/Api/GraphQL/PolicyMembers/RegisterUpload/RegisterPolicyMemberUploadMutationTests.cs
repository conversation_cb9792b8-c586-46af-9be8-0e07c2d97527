using AutoFixture;
using CoverGo.PoliciesV3.Api.GraphQL.PolicyMembers.RegisterUpload;
using CoverGo.PoliciesV3.Application.Features.PolicyMembers.RegisterUpload;
using FluentAssertions;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using System.Security.Claims;

namespace CoverGo.PoliciesV3.Tests.Unit.Api.GraphQL.PolicyMembers.RegisterUpload;

public class RegisterPolicyMemberUploadMutationTests
{
    private readonly Fixture _fixture;
    private readonly Mock<IMediator> _mediator;
    private readonly RegisterPolicyMemberUploadMutation _mutation;

    public RegisterPolicyMemberUploadMutationTests()
    {
        _fixture = new Fixture();
        _mediator = new Mock<IMediator>();
        _mutation = new RegisterPolicyMemberUploadMutation();
    }

    [Fact]
    public async Task RegisterPolicyMemberUpload_WithValidParameters_ShouldCallMediatorWithCorrectRequest()
    {
        // Arrange
        string policyId = Guid.NewGuid().ToString();
        string? endorsementId = Guid.NewGuid().ToString();
        string path = "uploads/test-file.csv";
        string tenantId = "test-tenant";
        ClaimsIdentity identity = CreateClaimsIdentity();
        CancellationToken cancellationToken = CancellationToken.None;

        var expectedResponse = new RegisterPolicyMemberUploadResponse
        {
            PolicyMemberUploadId = Guid.NewGuid(),
            PolicyId = Guid.Parse(policyId),
            EndorsementId = Guid.Parse(endorsementId),
            Path = path,
            MembersCount = 5,
            Status = "PENDING"
        };

        _mediator
            .Setup(x => x.Send(It.IsAny<RegisterPolicyMemberUploadRequest>(), cancellationToken))
            .ReturnsAsync(expectedResponse);

        // Act
        RegisterPolicyMemberUploadResponse result = await _mutation.RegisterPolicyMemberUpload(
            policyId,
            endorsementId,
            path,
            tenantId,
            identity,
            _mediator.Object,
            cancellationToken);

        // Assert
        result.Should().Be(expectedResponse);

        _mediator.Verify(x => x.Send(It.Is<RegisterPolicyMemberUploadRequest>(req =>
            req.PolicyId == Guid.Parse(policyId) &&
            req.EndorsementId == Guid.Parse(endorsementId) &&
            req.Path == path &&
            req.TenantId == tenantId &&
            req.Identity == identity
        ), cancellationToken), Times.Once);
    }

    [Fact]
    public async Task RegisterPolicyMemberUpload_WithNullEndorsementId_ShouldCallMediatorWithNullEndorsementId()
    {
        // Arrange
        string policyId = Guid.NewGuid().ToString();
        string? endorsementId = null;
        string path = "uploads/test-file.csv";
        string tenantId = "test-tenant";
        ClaimsIdentity identity = CreateClaimsIdentity();
        CancellationToken cancellationToken = CancellationToken.None;

        var expectedResponse = new RegisterPolicyMemberUploadResponse
        {
            PolicyMemberUploadId = Guid.NewGuid(),
            PolicyId = Guid.Parse(policyId),
            EndorsementId = null,
            Path = path,
            MembersCount = 3,
            Status = "PENDING"
        };

        _mediator
            .Setup(x => x.Send(It.IsAny<RegisterPolicyMemberUploadRequest>(), cancellationToken))
            .ReturnsAsync(expectedResponse);

        // Act
        RegisterPolicyMemberUploadResponse result = await _mutation.RegisterPolicyMemberUpload(
            policyId,
            endorsementId,
            path,
            tenantId,
            identity,
            _mediator.Object,
            cancellationToken);

        // Assert
        result.Should().Be(expectedResponse);

        _mediator.Verify(x => x.Send(It.Is<RegisterPolicyMemberUploadRequest>(req =>
            req.PolicyId == Guid.Parse(policyId) &&
            req.EndorsementId == null &&
            req.Path == path &&
            req.TenantId == tenantId &&
            req.Identity == identity
        ), cancellationToken), Times.Once);
    }

    [Theory]
    [InlineData("")]
    [InlineData("invalid-guid")]
    [InlineData("not-a-guid-at-all")]
    public async Task RegisterPolicyMemberUpload_WithInvalidPolicyId_ShouldThrowFormatException(string invalidPolicyId)
    {
        // Arrange
        string? endorsementId = null;
        string path = "uploads/test-file.csv";
        string tenantId = "test-tenant";
        ClaimsIdentity identity = CreateClaimsIdentity();
        CancellationToken cancellationToken = CancellationToken.None;

        // Act & Assert
        await Assert.ThrowsAsync<FormatException>(() => _mutation.RegisterPolicyMemberUpload(
            invalidPolicyId,
            endorsementId,
            path,
            tenantId,
            identity,
            _mediator.Object,
            cancellationToken));

        _mediator.Verify(x => x.Send(It.IsAny<RegisterPolicyMemberUploadRequest>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Theory]
    [InlineData("invalid-guid")]
    [InlineData("not-a-guid-at-all")]
    public async Task RegisterPolicyMemberUpload_WithInvalidEndorsementId_ShouldThrowFormatException(string invalidEndorsementId)
    {
        // Arrange
        string policyId = Guid.NewGuid().ToString();
        string path = "uploads/test-file.csv";
        string tenantId = "test-tenant";
        ClaimsIdentity identity = CreateClaimsIdentity();
        CancellationToken cancellationToken = CancellationToken.None;

        // Act & Assert
        await Assert.ThrowsAsync<FormatException>(() => _mutation.RegisterPolicyMemberUpload(
            policyId,
            invalidEndorsementId,
            path,
            tenantId,
            identity,
            _mediator.Object,
            cancellationToken));

        _mediator.Verify(x => x.Send(It.IsAny<RegisterPolicyMemberUploadRequest>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task RegisterPolicyMemberUpload_WithEmptyEndorsementId_ShouldTreatAsNull()
    {
        // Arrange
        string policyId = Guid.NewGuid().ToString();
        string endorsementId = ""; // Empty string should be treated as null
        string path = "uploads/test-file.csv";
        string tenantId = "test-tenant";
        ClaimsIdentity identity = CreateClaimsIdentity();
        CancellationToken cancellationToken = CancellationToken.None;

        var expectedResponse = new RegisterPolicyMemberUploadResponse
        {
            PolicyMemberUploadId = Guid.NewGuid(),
            PolicyId = Guid.Parse(policyId),
            EndorsementId = null,
            Path = path,
            MembersCount = 3,
            Status = "PENDING"
        };

        _mediator
            .Setup(x => x.Send(It.IsAny<RegisterPolicyMemberUploadRequest>(), cancellationToken))
            .ReturnsAsync(expectedResponse);

        // Act
        RegisterPolicyMemberUploadResponse result = await _mutation.RegisterPolicyMemberUpload(
            policyId,
            endorsementId,
            path,
            tenantId,
            identity,
            _mediator.Object,
            cancellationToken);

        // Assert
        result.Should().Be(expectedResponse);

        _mediator.Verify(x => x.Send(It.Is<RegisterPolicyMemberUploadRequest>(req =>
            req.PolicyId == Guid.Parse(policyId) &&
            req.EndorsementId == null && // Empty string should become null
            req.Path == path &&
            req.TenantId == tenantId &&
            req.Identity == identity
        ), cancellationToken), Times.Once);
    }

    [Fact]
    public async Task RegisterPolicyMemberUpload_WhenMediatorThrowsException_ShouldPropagateException()
    {
        // Arrange
        string policyId = Guid.NewGuid().ToString();
        string? endorsementId = null;
        string path = "uploads/test-file.csv";
        string tenantId = "test-tenant";
        ClaimsIdentity identity = CreateClaimsIdentity();
        CancellationToken cancellationToken = CancellationToken.None;

        var expectedException = new InvalidOperationException("Test exception");
        _mediator
            .Setup(x => x.Send(It.IsAny<RegisterPolicyMemberUploadRequest>(), cancellationToken))
            .ThrowsAsync(expectedException);

        // Act & Assert
        InvalidOperationException exception = await Assert.ThrowsAsync<InvalidOperationException>(() => 
            _mutation.RegisterPolicyMemberUpload(
                policyId,
                endorsementId,
                path,
                tenantId,
                identity,
                _mediator.Object,
                cancellationToken));

        exception.Should().Be(expectedException);
    }

    private static ClaimsIdentity CreateClaimsIdentity() => new(new[]
    {
        new Claim(ClaimTypes.NameIdentifier, Guid.NewGuid().ToString()),
        new Claim(ClaimTypes.Name, "test-user")
    });
}
