using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.Validators;
using CoverGo.PoliciesV3.Domain.CustomFields.Validation;

namespace CoverGo.PoliciesV3.Tests.Unit.Domain.CustomFields;

public class PolicyMemberFieldsSchemaTests
{
    private readonly IFixture _fixture;

    public PolicyMemberFieldsSchemaTests()
    {
        _fixture = new Fixture();
    }

    [Fact]
    public void MemberFields_WhenSet_ShouldReturnCorrectFields()
    {
        // Arrange
        List<PolicyMemberFieldDefinition> memberFields = CreateTestMemberFields();

        // Act
        var schema = new PolicyMemberFieldsSchema
        {
            MemberFields = memberFields
        };

        // Assert
        schema.MemberFields.Should().BeEquivalentTo(memberFields);
        schema.MemberCustomFields.Should().BeEquivalentTo(memberFields);
        schema.MemberSystemFields.Should().BeEmpty(); // No system fields in this test
    }

    [Fact]
    public void ProductFields_WhenSet_ShouldReturnCorrectFields()
    {
        // Arrange
        List<PolicyMemberFieldDefinition> productFields = CreateTestProductFields();
        List<PolicyMemberFieldDefinition> memberFields = CreateTestMemberFields();

        // Act
        var schema = new PolicyMemberFieldsSchema
        {
            MemberFields = memberFields,
            ProductFields = productFields
        };

        // Assert
        schema.ProductFields.Should().BeEquivalentTo(productFields);
    }

    [Fact]
    public void CensusFields_WhenSet_ShouldReturnCorrectFields()
    {
        // Arrange
        List<PolicyMemberFieldDefinition> censusFields = CreateTestCensusFields();
        List<PolicyMemberFieldDefinition> memberFields = CreateTestMemberFields();

        // Act
        var schema = new PolicyMemberFieldsSchema
        {
            MemberFields = memberFields,
            CensusFields = censusFields
        };

        // Assert
        schema.CensusFields.Should().BeEquivalentTo(censusFields);
    }

    [Fact]
    public void Fields_ShouldReturnAllFieldsWithCorrectPriority()
    {
        // Arrange
        List<PolicyMemberFieldDefinition> memberFields = CreateTestMemberFields();
        List<PolicyMemberFieldDefinition> productFields = CreateTestProductFields();
        List<PolicyMemberFieldDefinition> censusFields = CreateTestCensusFields();

        // Act
        var schema = new PolicyMemberFieldsSchema
        {
            MemberFields = memberFields,
            ProductFields = productFields,
            CensusFields = censusFields
        };

        // Assert
        IReadOnlyList<PolicyMemberFieldDefinition> allFields = schema.Fields;
        allFields.Should().NotBeEmpty();
        
        // Should contain all unique fields
        allFields.Should().Contain(f => f.Name == "firstName");
        allFields.Should().Contain(f => f.Name == "lastName");
        allFields.Should().Contain(f => f.Name == "productField1");
        allFields.Should().Contain(f => f.Name == "censusField1");
    }

    [Fact]
    public void Fields_WithDuplicateNames_ShouldPrioritizeMemberFields()
    {
        // Arrange
        var memberFields = new List<PolicyMemberFieldDefinition>
        {
            new()
            {
                Name = "duplicateField",
                Label = "Member Field",
                Type = new StringFieldType(),
                IsRequired = true,
                IsUnique = false
            }
        };

        var productFields = new List<PolicyMemberFieldDefinition>
        {
            new()
            {
                Name = "duplicateField",
                Label = "Product Field",
                Type = new StringFieldType(),
                IsRequired = false,
                IsUnique = false
            }
        };

        // Act
        var schema = new PolicyMemberFieldsSchema
        {
            MemberFields = memberFields,
            ProductFields = productFields
        };

        // Assert
        IReadOnlyList<PolicyMemberFieldDefinition> allFields = schema.Fields;
        PolicyMemberFieldDefinition duplicateField = allFields.First(f => f.Name == "duplicateField");
        duplicateField.Label.Should().Be("Member Field"); // Member field should take priority
        duplicateField.IsRequired.Should().BeTrue();
    }

    [Fact]
    public void GetField_WithExistingMemberField_ShouldReturnField()
    {
        // Arrange
        List<PolicyMemberFieldDefinition> memberFields = CreateTestMemberFields();
        var schema = new PolicyMemberFieldsSchema
        {
            MemberFields = memberFields
        };

        // Act
        PolicyMemberFieldDefinition? field = schema.GetField("firstName");

        // Assert
        field.Should().NotBeNull();
        field!.Name.Should().Be("firstName");
        field.Label.Should().Be("First Name");
    }

    [Fact]
    public void GetField_WithExistingProductField_ShouldReturnField()
    {
        // Arrange
        List<PolicyMemberFieldDefinition> memberFields = CreateTestMemberFields();
        List<PolicyMemberFieldDefinition> productFields = CreateTestProductFields();
        var schema = new PolicyMemberFieldsSchema
        {
            MemberFields = memberFields,
            ProductFields = productFields
        };

        // Act
        PolicyMemberFieldDefinition? field = schema.GetField("productField1");

        // Assert
        field.Should().NotBeNull();
        field!.Name.Should().Be("productField1");
        field.Label.Should().Be("Product Field 1");
    }

    [Fact]
    public void GetField_WithNonExistentField_ShouldReturnNull()
    {
        // Arrange
        List<PolicyMemberFieldDefinition> memberFields = CreateTestMemberFields();
        var schema = new PolicyMemberFieldsSchema
        {
            MemberFields = memberFields
        };

        // Act
        PolicyMemberFieldDefinition? field = schema.GetField("nonExistentField");

        // Assert
        field.Should().BeNull();
    }

    [Fact]
    public void GetField_WithDuplicateNames_ShouldReturnMemberFieldFirst()
    {
        // Arrange
        var memberFields = new List<PolicyMemberFieldDefinition>
        {
            new()
            {
                Name = "duplicateField",
                Label = "Member Field",
                Type = new StringFieldType(),
                IsRequired = true,
                IsUnique = false
            }
        };

        var productFields = new List<PolicyMemberFieldDefinition>
        {
            new()
            {
                Name = "duplicateField",
                Label = "Product Field",
                Type = new StringFieldType(),
                IsRequired = false,
                IsUnique = false
            }
        };

        var schema = new PolicyMemberFieldsSchema
        {
            MemberFields = memberFields,
            ProductFields = productFields
        };

        // Act
        PolicyMemberFieldDefinition? field = schema.GetField("duplicateField");

        // Assert
        field.Should().NotBeNull();
        field!.Label.Should().Be("Member Field"); // Member field should take priority
    }

    [Fact]
    public void OneOfValidations_WhenSet_ShouldReturnCorrectValidations()
    {
        // Arrange
        List<PolicyMemberFieldDefinition> memberFields = CreateTestMemberFields();
        List<CustomFieldOneOfValidation> oneOfValidations = CreateTestOneOfValidations(memberFields);

        // Act
        var schema = new PolicyMemberFieldsSchema
        {
            MemberFields = memberFields,
            OneOfValidations = oneOfValidations
        };

        // Assert
        schema.OneOfValidations.Should().BeEquivalentTo(oneOfValidations);
    }

    [Fact]
    public void Schema_WithNullCollections_ShouldHandleGracefully()
    {
        // Arrange & Act
        var schema = new PolicyMemberFieldsSchema
        {
            MemberFields = [],
            ProductFields = null,
            CensusFields = null,
            OneOfValidations = null
        };

        // Assert
        schema.MemberFields.Should().BeEmpty();
        schema.ProductFields.Should().BeNull();
        schema.CensusFields.Should().BeNull();
        schema.OneOfValidations.Should().BeNull();
        schema.Fields.Should().BeEmpty();
    }

    [Fact]
    public void Schema_WithEmptyCollections_ShouldReturnEmptyResults()
    {
        // Arrange & Act
        var schema = new PolicyMemberFieldsSchema
        {
            MemberFields = [],
            ProductFields = [],
            CensusFields = [],
            OneOfValidations = []
        };

        // Assert
        schema.MemberFields.Should().BeEmpty();
        schema.ProductFields.Should().BeEmpty();
        schema.CensusFields.Should().BeEmpty();
        schema.OneOfValidations.Should().BeEmpty();
        schema.Fields.Should().BeEmpty();
    }

    private static List<PolicyMemberFieldDefinition> CreateTestMemberFields() =>
        [
            new()
            {
                Name = "firstName",
                Label = "First Name",
                Type = new StringFieldType(),
                IsRequired = true,
                IsUnique = false
            },
            new()
            {
                Name = "lastName",
                Label = "Last Name",
                Type = new StringFieldType(),
                IsRequired = true,
                IsUnique = false
            }
        ];

    private static List<PolicyMemberFieldDefinition> CreateTestProductFields() =>
        [
            new()
            {
                Name = "productField1",
                Label = "Product Field 1",
                Type = new StringFieldType(),
                IsRequired = false,
                IsUnique = false
            }
        ];

    private static List<PolicyMemberFieldDefinition> CreateTestCensusFields() =>
        [
            new()
            {
                Name = "censusField1",
                Label = "Census Field 1",
                Type = new DateFieldType(),
                IsRequired = false,
                IsUnique = false
            }
        ];

    private static List<CustomFieldOneOfValidation> CreateTestOneOfValidations(List<PolicyMemberFieldDefinition> memberFields)
    {
        var validations = memberFields.Select(field => new CustomFieldRequiredValidation
        {
            Field = field,
            IsRequired = true
        }).ToList();

        return
        [
            new()
            {
                Validations = validations
            }
        ];
    }
}
