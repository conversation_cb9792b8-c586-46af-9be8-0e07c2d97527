using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.ValidationErrors;

namespace CoverGo.PoliciesV3.Tests.Unit.Domain.CustomFields.FieldTypes;

public class DateFieldTypeAdvancedValidationTests
{
    private readonly IFixture _fixture;

    public DateFieldTypeAdvancedValidationTests()
    {
        _fixture = new Fixture();
    }

    [Fact]
    public void TryParseField_WithMinEmployeeAgeValidation_WhenEmployeeTooYoung_ShouldReturnError()
    {
        // Arrange
        var fieldType = new DateFieldType { Validations = "minEmployeeAge:18" };
        PolicyMemberFieldDefinition field = CreateTestField();
        
        // 16 years old (too young)
        var birthDate = DateOnly.FromDateTime(DateTime.Now.AddYears(-16));
        var otherFields = new Dictionary<string, object?>
        {
            { "memberType", "employee" }
        };

        // Act
        List<IFieldValidationError>? result = fieldType.TryParseField(birthDate, field, out object? parsedValue, otherFields);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(1);
        result![0].Should().BeOfType<FieldMinAgeException>();
        var error = (FieldMinAgeException)result[0];
        error.PropertyPath.Should().Be(field.Name);
        error.PropertyLabel.Should().Be(field.Label);
        error.Message.Should().Contain("18 years old");
    }

    [Fact]
    public void TryParseField_WithMinEmployeeAgeValidation_WhenEmployeeOldEnough_ShouldReturnNull()
    {
        // Arrange
        var fieldType = new DateFieldType { Validations = "minEmployeeAge:18" };
        PolicyMemberFieldDefinition field = CreateTestField();
        
        // 25 years old (old enough)
        var birthDate = DateOnly.FromDateTime(DateTime.Now.AddYears(-25));
        var otherFields = new Dictionary<string, object?>
        {
            { "memberType", "employee" }
        };

        // Act
        List<IFieldValidationError>? result = fieldType.TryParseField(birthDate, field, out object? parsedValue, otherFields);

        // Assert
        result.Should().BeNull();
        parsedValue.Should().Be(birthDate);
    }

    [Fact]
    public void TryParseField_WithMaxEmployeeAgeValidation_WhenEmployeeTooOld_ShouldReturnError()
    {
        // Arrange
        var fieldType = new DateFieldType { Validations = "maxEmployeeAge:65" };
        PolicyMemberFieldDefinition field = CreateTestField();
        
        // 70 years old (too old)
        var birthDate = DateOnly.FromDateTime(DateTime.Now.AddYears(-70));
        var otherFields = new Dictionary<string, object?>
        {
            { "memberType", "employee" }
        };

        // Act
        List<IFieldValidationError>? result = fieldType.TryParseField(birthDate, field, out object? parsedValue, otherFields);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(1);
        result![0].Should().BeOfType<FieldMaxAgeException>();
        var error = (FieldMaxAgeException)result[0];
        error.PropertyPath.Should().Be(field.Name);
        error.PropertyLabel.Should().Be(field.Label);
        error.Message.Should().Contain("65 years old");
    }

    [Fact]
    public void TryParseField_WithMinChildDaysValidation_WhenChildTooYoung_ShouldReturnError()
    {
        // Arrange
        var fieldType = new DateFieldType { Validations = "minChildDays:30" };
        PolicyMemberFieldDefinition field = CreateTestField();
        
        // 15 days old (too young)
        var birthDate = DateOnly.FromDateTime(DateTime.Now.AddDays(-15));
        var otherFields = new Dictionary<string, object?>
        {
            { "relationshipToEmployee", "child" }
        };

        // Act
        List<IFieldValidationError>? result = fieldType.TryParseField(birthDate, field, out object? parsedValue, otherFields);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(1);
        result![0].Should().BeOfType<FieldMinChildDaysException>();
        var error = (FieldMinChildDaysException)result[0];
        error.PropertyPath.Should().Be(field.Name);
        error.PropertyLabel.Should().Be(field.Label);
        error.Message.Should().Contain("30 days");
    }

    [Fact]
    public void TryParseField_WithMinSpouseAgeValidation_WhenSpouseTooYoung_ShouldReturnError()
    {
        // Arrange
        var fieldType = new DateFieldType { Validations = "minSpouseAge:18" };
        PolicyMemberFieldDefinition field = CreateTestField();
        
        // 16 years old (too young)
        var birthDate = DateOnly.FromDateTime(DateTime.Now.AddYears(-16));
        var otherFields = new Dictionary<string, object?>
        {
            { "relationshipToEmployee", "spouse" }
        };

        // Act
        List<IFieldValidationError>? result = fieldType.TryParseField(birthDate, field, out object? parsedValue, otherFields);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(1);
        result![0].Should().BeOfType<FieldMinSpouseAgeException>();
        var error = (FieldMinSpouseAgeException)result[0];
        error.PropertyPath.Should().Be(field.Name);
        error.PropertyLabel.Should().Be(field.Label);
        error.Message.Should().Contain("18 years old");
    }

    [Fact]
    public void TryParseField_WithEmployeeValidation_WhenNotEmployee_ShouldNotApplyValidation()
    {
        // Arrange
        var fieldType = new DateFieldType { Validations = "minEmployeeAge:18" };
        PolicyMemberFieldDefinition field = CreateTestField();
        
        // 16 years old but not an employee
        var birthDate = DateOnly.FromDateTime(DateTime.Now.AddYears(-16));
        var otherFields = new Dictionary<string, object?>
        {
            { "memberType", "dependent" } // Not an employee
        };

        // Act
        List<IFieldValidationError>? result = fieldType.TryParseField(birthDate, field, out object? parsedValue, otherFields);

        // Assert
        result.Should().BeNull(); // Should not apply employee validation to non-employees
        parsedValue.Should().Be(birthDate);
    }

    [Fact]
    public void TryParseField_WithMultipleValidations_ShouldApplyAllApplicable()
    {
        // Arrange
        var fieldType = new DateFieldType { Validations = "minEmployeeAge:18|maxEmployeeAge:65" };
        PolicyMemberFieldDefinition field = CreateTestField();
        
        // 16 years old (violates min age)
        var birthDate = DateOnly.FromDateTime(DateTime.Now.AddYears(-16));
        var otherFields = new Dictionary<string, object?>
        {
            { "memberType", "employee" }
        };

        // Act
        List<IFieldValidationError>? result = fieldType.TryParseField(birthDate, field, out object? parsedValue, otherFields);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(1); // Should return first validation error
        result![0].Should().BeOfType<FieldMinAgeException>();
    }

    [Fact]
    public void TryParseField_WithNoOtherFields_ShouldNotApplyContextValidation()
    {
        // Arrange
        var fieldType = new DateFieldType { Validations = "minEmployeeAge:18" };
        PolicyMemberFieldDefinition field = CreateTestField();
        
        // 16 years old
        var birthDate = DateOnly.FromDateTime(DateTime.Now.AddYears(-16));

        // Act
        List<IFieldValidationError>? result = fieldType.TryParseField(birthDate, field, out object? parsedValue, null);

        // Assert
        result.Should().BeNull(); // Should not apply validation without context
        parsedValue.Should().Be(birthDate);
    }

    private static PolicyMemberFieldDefinition CreateTestField() => new()
    {
        Name = "testDateField",
        Label = "Test Date Field",
        Type = new DateFieldType(),
        IsRequired = false,
        IsUnique = false
    };
}
