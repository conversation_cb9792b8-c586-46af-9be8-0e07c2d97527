using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.ValidationErrors;

namespace CoverGo.PoliciesV3.Tests.Unit.Domain.CustomFields.FieldTypes;

public class DateFieldTypeTests
{
    private readonly IFixture _fixture;

    public DateFieldTypeTests()
    {
        _fixture = new Fixture();
    }

    [Fact]
    public void ValidateField_WithNullValue_ShouldReturnNull()
    {
        // Arrange
        var fieldType = new DateFieldType();
        PolicyMemberFieldDefinition field = CreateTestField();

        // Act
        List<IFieldValidationError>? result = fieldType.ValidateField(null, field);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public void ValidateField_WithDateTimeValue_ShouldReturnNull()
    {
        // Arrange
        var fieldType = new DateFieldType();
        PolicyMemberFieldDefinition field = CreateTestField();
        DateTime value = DateTime.Now;

        // Act
        List<IFieldValidationError>? result = fieldType.ValidateField(value, field);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public void ValidateField_WithDateOnlyValue_ShouldReturnNull()
    {
        // Arrange
        var fieldType = new DateFieldType();
        PolicyMemberFieldDefinition field = CreateTestField();
        var value = DateOnly.FromDateTime(DateTime.Now);

        // Act
        List<IFieldValidationError>? result = fieldType.ValidateField(value, field);

        // Assert
        result.Should().BeNull();
    }

    [Theory]
    [InlineData("2024-01-01")]
    [InlineData("2024-12-31")]
    [InlineData("01/01/2024")]
    [InlineData("January 1, 2024")]
    public void ValidateField_WithValidDateString_ShouldReturnNull(string dateString)
    {
        // Arrange
        var fieldType = new DateFieldType();
        PolicyMemberFieldDefinition field = CreateTestField();

        // Act
        List<IFieldValidationError>? result = fieldType.ValidateField(dateString, field);

        // Assert
        result.Should().BeNull();
    }

    [Theory]
    [InlineData("invalid-date")]
    [InlineData("2024-13-01")] // Invalid month
    [InlineData("2024-01-32")] // Invalid day
    [InlineData("not a date")]
    [InlineData("")]
    public void ValidateField_WithInvalidDateString_ShouldReturnError(string invalidDateString)
    {
        // Arrange
        var fieldType = new DateFieldType();
        PolicyMemberFieldDefinition field = CreateTestField();

        // Act
        List<IFieldValidationError>? result = fieldType.ValidateField(invalidDateString, field);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(1);
        result![0].Should().BeOfType<FieldInvalidDateException>();
        var error = (FieldInvalidDateException)result[0];
        error.PropertyPath.Should().Be(field.Name);
        error.PropertyLabel.Should().Be(field.Label);
    }

    [Fact]
    public void ValidateField_WithNonStringNonDateValue_ShouldReturnTypeError()
    {
        // Arrange
        var fieldType = new DateFieldType();
        PolicyMemberFieldDefinition field = CreateTestField();
        int value = 123; // Non-date value

        // Act
        List<IFieldValidationError>? result = fieldType.ValidateField(value, field);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(1);
        result![0].Should().BeOfType<FieldInvalidTypeException>();
        var error = (FieldInvalidTypeException)result[0];
        error.PropertyPath.Should().Be(field.Name);
        error.PropertyLabel.Should().Be(field.Label);
    }

    [Fact]
    public void TryParseField_WithNullValue_ShouldReturnNullAndNullParsedValue()
    {
        // Arrange
        var fieldType = new DateFieldType();
        PolicyMemberFieldDefinition field = CreateTestField();

        // Act
        List<IFieldValidationError>? result = fieldType.TryParseField(null, field, out object? parsedValue);

        // Assert
        result.Should().BeNull();
        parsedValue.Should().BeNull();
    }

    [Fact]
    public void TryParseField_WithValidDateString_ShouldReturnNullAndParsedDateOnly()
    {
        // Arrange
        var fieldType = new DateFieldType();
        PolicyMemberFieldDefinition field = CreateTestField();
        string dateString = "2024-01-01";
        var expectedDate = new DateOnly(2024, 1, 1);

        // Act
        List<IFieldValidationError>? result = fieldType.TryParseField(dateString, field, out object? parsedValue);

        // Assert
        result.Should().BeNull();
        parsedValue.Should().Be(expectedDate);
    }

    [Fact]
    public void TryParseField_WithDateTime_ShouldReturnNullAndConvertToDateOnly()
    {
        // Arrange
        var fieldType = new DateFieldType();
        PolicyMemberFieldDefinition field = CreateTestField();
        var dateTime = new DateTime(2024, 1, 1, 15, 30, 45);
        var expectedDate = new DateOnly(2024, 1, 1);

        // Act
        List<IFieldValidationError>? result = fieldType.TryParseField(dateTime, field, out object? parsedValue);

        // Assert
        result.Should().BeNull();
        parsedValue.Should().Be(expectedDate);
    }

    [Fact]
    public void TryParseField_WithDateOnly_ShouldReturnNullAndSameDateOnly()
    {
        // Arrange
        var fieldType = new DateFieldType();
        PolicyMemberFieldDefinition field = CreateTestField();
        var dateOnly = new DateOnly(2024, 1, 1);

        // Act
        List<IFieldValidationError>? result = fieldType.TryParseField(dateOnly, field, out object? parsedValue);

        // Assert
        result.Should().BeNull();
        parsedValue.Should().Be(dateOnly);
    }

    [Fact]
    public void TryParseField_WithInvalidDateString_ShouldReturnErrorAndNullParsedValue()
    {
        // Arrange
        var fieldType = new DateFieldType();
        PolicyMemberFieldDefinition field = CreateTestField();
        string invalidDateString = "invalid-date";

        // Act
        List<IFieldValidationError>? result = fieldType.TryParseField(invalidDateString, field, out object? parsedValue);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(1);
        result![0].Should().BeOfType<FieldInvalidDateException>();
        parsedValue.Should().Be(invalidDateString); // Should return original value when validation fails
    }

    [Fact]
    public void TryParseField_WithNonDateValue_ShouldReturnErrorAndOriginalValue()
    {
        // Arrange
        var fieldType = new DateFieldType();
        PolicyMemberFieldDefinition field = CreateTestField();
        int nonDateValue = 123;

        // Act
        List<IFieldValidationError>? result = fieldType.TryParseField(nonDateValue, field, out object? parsedValue);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(1);
        result![0].Should().BeOfType<FieldInvalidTypeException>();
        parsedValue.Should().Be(nonDateValue); // Should return original value when parsing fails
    }

    [Theory]
    [InlineData("2024-02-29")] // Valid leap year date
    [InlineData("2023-02-28")] // Valid non-leap year date
    [InlineData("2024-12-31")] // End of year
    [InlineData("2024-01-01")] // Start of year
    public void TryParseField_WithEdgeCaseDates_ShouldParseCorrectly(string dateString)
    {
        // Arrange
        var fieldType = new DateFieldType();
        PolicyMemberFieldDefinition field = CreateTestField();

        // Act
        List<IFieldValidationError>? result = fieldType.TryParseField(dateString, field, out object? parsedValue);

        // Assert
        result.Should().BeNull();
        parsedValue.Should().NotBeNull();
        parsedValue.Should().BeOfType<DateOnly>();
    }

    [Fact]
    public void TryParseField_WithDifferentDateFormats_ShouldParseCorrectly()
    {
        // Arrange
        var fieldType = new DateFieldType();
        PolicyMemberFieldDefinition field = CreateTestField();
        var expectedDate = new DateOnly(2024, 1, 15);

        string[] dateFormats = new[]
        {
            "2024-01-15",
            "01/15/2024",
            "January 15, 2024",
            "15 Jan 2024"
        };

        foreach (string? dateFormat in dateFormats)
        {
            // Act
            List<IFieldValidationError>? result = fieldType.TryParseField(dateFormat, field, out object? parsedValue);

            // Assert
            result.Should().BeNull($"Date format '{dateFormat}' should be valid");
            if (parsedValue is DateOnly actualDate)
            {
                actualDate.Should().Be(expectedDate, $"Date format '{dateFormat}' should parse to {expectedDate}");
            }
        }
    }

    private static PolicyMemberFieldDefinition CreateTestField() => new()
    {
        Name = "testDateField",
        Label = "Test Date Field",
        Type = new DateFieldType(),
        IsRequired = false,
        IsUnique = false
    };
}
