using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.ValidationErrors;

namespace CoverGo.PoliciesV3.Tests.Unit.Domain.CustomFields.FieldTypes;

public class FieldTypeTests
{
    #region BooleanFieldType Tests

    [Fact]
    public void BooleanFieldType_ValidateField_WithValidBooleanTrue_ShouldReturnNull()
    {
        // Arrange
        var fieldType = new BooleanFieldType();
        PolicyMemberFieldDefinition field = CreateTestField("testBooleanField");
        bool value = true;

        // Act
        List<IFieldValidationError>? result = fieldType.ValidateField(value, field);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public void BooleanFieldType_ValidateField_WithValidBooleanFalse_ShouldReturnNull()
    {
        // Arrange
        var fieldType = new BooleanFieldType();
        PolicyMemberFieldDefinition field = CreateTestField("testBooleanField");
        bool value = false;

        // Act
        List<IFieldValidationError>? result = fieldType.ValidateField(value, field);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public void BooleanFieldType_ValidateField_WithStringTrue_ShouldReturnNull()
    {
        // Arrange
        var fieldType = new BooleanFieldType();
        PolicyMemberFieldDefinition field = CreateTestField("testBooleanField");
        string value = "true";

        // Act
        List<IFieldValidationError>? result = fieldType.ValidateField(value, field);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public void BooleanFieldType_ValidateField_WithStringFalse_ShouldReturnNull()
    {
        // Arrange
        var fieldType = new BooleanFieldType();
        PolicyMemberFieldDefinition field = CreateTestField("testBooleanField");
        string value = "false";

        // Act
        List<IFieldValidationError>? result = fieldType.ValidateField(value, field);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public void BooleanFieldType_ValidateField_WithInvalidString_ShouldReturnError()
    {
        // Arrange
        var fieldType = new BooleanFieldType();
        PolicyMemberFieldDefinition field = CreateTestField("testBooleanField");
        string value = "invalid";

        // Act
        List<IFieldValidationError>? result = fieldType.ValidateField(value, field);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(1);
        result![0].Should().BeOfType<FieldInvalidTypeException>();
    }

    [Fact]
    public void BooleanFieldType_ValidateField_WithNullValue_ShouldReturnNull()
    {
        // Arrange
        var fieldType = new BooleanFieldType();
        PolicyMemberFieldDefinition field = CreateTestField("testBooleanField");
        object? value = null;

        // Act
        List<IFieldValidationError>? result = fieldType.ValidateField(value, field);

        // Assert
        result.Should().BeNull();
    }

    #endregion

    #region NumberFieldType Tests

    [Fact]
    public void NumberFieldType_ValidateField_WithValidInteger_ShouldReturnNull()
    {
        // Arrange
        var fieldType = new NumberFieldType();
        PolicyMemberFieldDefinition field = CreateTestField("testNumberField");
        int value = 42;

        // Act
        List<IFieldValidationError>? result = fieldType.ValidateField(value, field);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public void NumberFieldType_ValidateField_WithValidDecimal_ShouldReturnNull()
    {
        // Arrange
        var fieldType = new NumberFieldType();
        PolicyMemberFieldDefinition field = CreateTestField("testNumberField");
        decimal value = 42.5m;

        // Act
        List<IFieldValidationError>? result = fieldType.ValidateField(value, field);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public void NumberFieldType_ValidateField_WithValidStringNumber_ShouldReturnNull()
    {
        // Arrange
        var fieldType = new NumberFieldType();
        PolicyMemberFieldDefinition field = CreateTestField("testNumberField");
        string value = "42.5";

        // Act
        List<IFieldValidationError>? result = fieldType.ValidateField(value, field);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public void NumberFieldType_ValidateField_WithInvalidString_ShouldReturnError()
    {
        // Arrange
        var fieldType = new NumberFieldType();
        PolicyMemberFieldDefinition field = CreateTestField("testNumberField");
        string value = "not-a-number";

        // Act
        List<IFieldValidationError>? result = fieldType.ValidateField(value, field);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(1);
        result![0].Should().BeOfType<FieldInvalidNumberException>();
    }

    [Fact]
    public void NumberFieldType_ValidateField_WithNullValue_ShouldReturnNull()
    {
        // Arrange
        var fieldType = new NumberFieldType();
        PolicyMemberFieldDefinition field = CreateTestField("testNumberField");
        object? value = null;

        // Act
        List<IFieldValidationError>? result = fieldType.ValidateField(value, field);

        // Assert
        result.Should().BeNull();
    }

    #endregion

    #region FilesFieldType Tests

    [Fact]
    public void FilesFieldType_ValidateField_WithValidFilesList_ShouldReturnNull()
    {
        // Arrange
        var fieldType = new FilesFieldType();
        PolicyMemberFieldDefinition field = CreateTestField("testFilesField");
        var value = new List<Dictionary<string, object>>
        {
            new() { { "fileId", "12345" }, { "fileName", "document.pdf" } }
        };

        // Act
        List<IFieldValidationError>? result = fieldType.ValidateField(value, field);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public void FilesFieldType_ValidateField_WithNullValue_ShouldReturnNull()
    {
        // Arrange
        var fieldType = new FilesFieldType();
        PolicyMemberFieldDefinition field = CreateTestField("testFilesField");
        object? value = null;

        // Act
        List<IFieldValidationError>? result = fieldType.ValidateField(value, field);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public void FilesFieldType_ValidateField_WithInvalidType_ShouldReturnError()
    {
        // Arrange
        var fieldType = new FilesFieldType();
        PolicyMemberFieldDefinition field = CreateTestField("testFilesField");
        var value = new { fileId = "12345", fileName = "document.pdf" }; // Invalid: not a List<Dictionary<string, object>>

        // Act
        List<IFieldValidationError>? result = fieldType.ValidateField(value, field);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(1);
        result![0].Should().BeOfType<FieldInvalidTypeException>();
    }

    #endregion

    #region AddressFieldType Tests

    [Fact]
    public void AddressFieldType_ValidateField_WithValidAddress_ShouldReturnNull()
    {
        // Arrange
        var innerFields = new List<PolicyMemberFieldDefinition>
        {
            new()
            {
                Name = "street",
                Label = "Street",
                Type = new StringFieldType(),
                IsRequired = true,
                IsUnique = false
            },
            new()
            {
                Name = "city",
                Label = "City", 
                Type = new StringFieldType(),
                IsRequired = true,
                IsUnique = false
            }
        };
        
        var fieldType = new AddressFieldType(innerFields);
        PolicyMemberFieldDefinition field = CreateTestField("testAddressField");
        
        var value = new Dictionary<string, object?>
        {
            ["street"] = "123 Main St",
            ["city"] = "New York"
        };

        // Act
        List<IFieldValidationError>? result = fieldType.ValidateField(value, field);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public void AddressFieldType_ValidateField_WithNullValue_ShouldReturnNull()
    {
        // Arrange
        var innerFields = new List<PolicyMemberFieldDefinition>();
        var fieldType = new AddressFieldType(innerFields);
        PolicyMemberFieldDefinition field = CreateTestField("testAddressField");
        object? value = null;

        // Act
        List<IFieldValidationError>? result = fieldType.ValidateField(value, field);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public void AddressFieldType_ValidateField_WithExtraFields_ShouldReturnError()
    {
        // Arrange
        var innerFields = new List<PolicyMemberFieldDefinition>
        {
            new()
            {
                Name = "street",
                Label = "Street",
                Type = new StringFieldType(),
                IsRequired = true,
                IsUnique = false
            },
            new()
            {
                Name = "city",
                Label = "City",
                Type = new StringFieldType(),
                IsRequired = true,
                IsUnique = false
            }
        };

        var fieldType = new AddressFieldType(innerFields);
        PolicyMemberFieldDefinition field = CreateTestField("testAddressField");

        // Address with extra field that's not defined
        var value = new Dictionary<string, object?>
        {
            ["street"] = "123 Main St",
            ["city"] = "New York",
            ["extraField"] = "This should not be allowed" // Extra field
        };

        // Act
        List<IFieldValidationError>? result = fieldType.ValidateField(value, field);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(1);
        result![0].Should().BeOfType<FieldNoExtraAllowedException>();
        var error = (FieldNoExtraAllowedException)result[0];
        error.PropertyPath.Should().Be("extraField");
    }

    #endregion

    #region Helper Methods

    private static PolicyMemberFieldDefinition CreateTestField(string name) => new()
    {
        Name = name,
        Label = $"Test {name} Field",
        Type = new StringFieldType(),
        IsRequired = false,
        IsUnique = false
    };

    #endregion
}
