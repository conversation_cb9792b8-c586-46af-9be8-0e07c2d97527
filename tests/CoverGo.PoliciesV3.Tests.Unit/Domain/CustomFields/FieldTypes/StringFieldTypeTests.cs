using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.ValidationErrors;

namespace CoverGo.PoliciesV3.Tests.Unit.Domain.CustomFields.FieldTypes;

public class StringFieldTypeTests
{
    private readonly IFixture _fixture;

    public StringFieldTypeTests()
    {
        _fixture = new Fixture();
    }

    [Fact]
    public void ValidateField_WithNullValue_ShouldReturnNull()
    {
        // Arrange
        var fieldType = new StringFieldType { Options = null, Validations = null };
        PolicyMemberFieldDefinition field = CreateTestField();

        // Act
        List<IFieldValidationError>? result = fieldType.ValidateField(null, field);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public void ValidateField_WithValidStringValue_ShouldReturnNull()
    {
        // Arrange
        var fieldType = new StringFieldType { Options = null, Validations = null };
        PolicyMemberFieldDefinition field = CreateTestField();
        string value = "Valid string value";

        // Act
        List<IFieldValidationError>? result = fieldType.ValidateField(value, field);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public void ValidateField_WithNonStringValue_ShouldReturnTypeError()
    {
        // Arrange
        var fieldType = new StringFieldType { Options = null, Validations = null };
        PolicyMemberFieldDefinition field = CreateTestField();
        int value = 123; // Non-string value

        // Act
        List<IFieldValidationError>? result = fieldType.ValidateField(value, field);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(1);
        result![0].Should().BeOfType<FieldInvalidTypeException>();
        var error = (FieldInvalidTypeException)result[0];
        error.PropertyPath.Should().Be(field.Name);
        error.PropertyLabel.Should().Be(field.Label);
    }

    [Fact]
    public void ValidateField_WithValidOption_ShouldReturnNull()
    {
        // Arrange
        var options = new List<StringOption>
        {
            new() { Value = "Option1", Label = "Option 1" },
            new() { Value = "Option2", Label = "Option 2" }
        };
        var fieldType = new StringFieldType { Options = options, Validations = null };
        PolicyMemberFieldDefinition field = CreateTestField();
        string value = "Option1";

        // Act
        List<IFieldValidationError>? result = fieldType.ValidateField(value, field);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public void ValidateField_WithInvalidOption_ShouldReturnOptionError()
    {
        // Arrange
        var options = new List<StringOption>
        {
            new() { Value = "Option1", Label = "Option 1" },
            new() { Value = "Option2", Label = "Option 2" }
        };
        var fieldType = new StringFieldType { Options = options, Validations = null };
        PolicyMemberFieldDefinition field = CreateTestField();
        string value = "InvalidOption";

        // Act
        List<IFieldValidationError>? result = fieldType.ValidateField(value, field);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(1);
        result![0].Should().BeOfType<FieldInvalidStringOptionException>();
        var error = (FieldInvalidStringOptionException)result[0];
        error.PropertyPath.Should().Be(field.Name);
        error.PropertyLabel.Should().Be(field.Label);
        error.Options.Should().BeEquivalentTo(options);
    }

    [Fact]
    public void ValidateField_WithEmptyStringAndOptions_ShouldReturnNull()
    {
        // Arrange
        var options = new List<StringOption>
        {
            new() { Value = "Option1", Label = "Option 1" }
        };
        var fieldType = new StringFieldType { Options = options, Validations = null };
        PolicyMemberFieldDefinition field = CreateTestField();
        string value = "";

        // Act
        List<IFieldValidationError>? result = fieldType.ValidateField(value, field);

        // Assert
        result.Should().BeNull(); // Empty string should not be validated against options
    }

    [Theory]
    [InlineData("123", true)]
    [InlineData("123.45", true)]
    [InlineData("-123", true)]
    [InlineData("-123.45", true)]
    [InlineData("abc", false)]
    [InlineData("123abc", false)]
    [InlineData("", false)]
    public void ValidateField_WithNumberValidation_ShouldValidateCorrectly(string value, bool shouldBeValid)
    {
        // Arrange
        var fieldType = new StringFieldType { Options = null, Validations = "number" };
        PolicyMemberFieldDefinition field = CreateTestField();

        // Act
        List<IFieldValidationError>? result = fieldType.ValidateField(value, field);

        // Assert
        if (shouldBeValid)
        {
            result.Should().BeNull();
        }
        else
        {
            result.Should().NotBeNull();
            result.Should().HaveCount(1);
            result![0].Should().BeOfType<FieldInvalidNumberException>();
        }
    }

    [Theory]
    [InlineData("regex:^[A-Z]+$", "ABC", true)]
    [InlineData("regex:^[A-Z]+$", "abc", false)]
    [InlineData("/^[0-9]+$/", "123", true)]
    [InlineData("/^[0-9]+$/", "abc", false)]
    public void ValidateField_WithRegexValidation_ShouldValidateCorrectly(string validation, string value, bool shouldBeValid)
    {
        // Arrange
        var fieldType = new StringFieldType { Options = null, Validations = validation };
        PolicyMemberFieldDefinition field = CreateTestField();

        // Act
        List<IFieldValidationError>? result = fieldType.ValidateField(value, field);

        // Assert
        if (shouldBeValid)
        {
            result.Should().BeNull();
        }
        else
        {
            result.Should().NotBeNull();
            result.Should().HaveCount(1);
            result![0].Should().BeOfType<FieldInvalidStringException>();
            var error = (FieldInvalidStringException)result[0];
            error.Validation.Should().Be(validation);
        }
    }

    [Fact]
    public void ValidateField_WithMultipleValidations_ShouldValidateAll()
    {
        // Arrange
        var fieldType = new StringFieldType { Options = null, Validations = "number|regex:^[0-9]+$" };
        PolicyMemberFieldDefinition field = CreateTestField();
        string value = "123";

        // Act
        List<IFieldValidationError>? result = fieldType.ValidateField(value, field);

        // Assert
        result.Should().BeNull(); // Should pass both number and regex validation
    }

    [Fact]
    public void ValidateField_WithMultipleValidationsFirstFails_ShouldReturnFirstError()
    {
        // Arrange
        var fieldType = new StringFieldType { Options = null, Validations = "number|regex:^[A-Z]+$" };
        PolicyMemberFieldDefinition field = CreateTestField();
        string value = "abc"; // Fails number validation

        // Act
        List<IFieldValidationError>? result = fieldType.ValidateField(value, field);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(1);
        result![0].Should().BeOfType<FieldInvalidNumberException>();
    }

    [Fact]
    public void TryParseField_ShouldCallValidateField()
    {
        // Arrange
        var fieldType = new StringFieldType { Options = null, Validations = null };
        PolicyMemberFieldDefinition field = CreateTestField();
        string value = "test value";

        // Act
        List<IFieldValidationError>? result = fieldType.TryParseField(value, field, out object? parsedValue);

        // Assert
        result.Should().BeNull();
        parsedValue.Should().Be(value); // Should return the same value
    }

    [Fact]
    public void StringOption_ShouldHaveCorrectProperties()
    {
        // Arrange & Act
        var option = new StringOption { Value = "TestValue", Label = "Test Label" };

        // Assert
        option.Value.Should().Be("TestValue");
        option.Label.Should().Be("Test Label");
    }

    [Fact]
    public void StringOption_WithNullLabel_ShouldBeValid()
    {
        // Arrange & Act
        var option = new StringOption { Value = "TestValue", Label = null };

        // Assert
        option.Value.Should().Be("TestValue");
        option.Label.Should().BeNull();
    }

    private static PolicyMemberFieldDefinition CreateTestField() => new()
    {
        Name = "testField",
        Label = "Test Field",
        Type = new StringFieldType(),
        IsRequired = false,
        IsUnique = false
    };
}
