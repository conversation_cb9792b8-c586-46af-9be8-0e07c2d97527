using CoverGo.PoliciesV3.Application.Common.Interfaces;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Exceptions;
using CoverGo.PoliciesV3.Infrastructure.FileParser;
using System.Text;

namespace CoverGo.PoliciesV3.Tests.Unit.Infrastructure.FileParser;

[Trait("Category", "Unit")]
[Trait("Component", "Infrastructure")]
[Trait("Feature", "FileParser")]
public class CsvFileParserTests
{
    private readonly CsvFileParser _parser;

    public CsvFileParserTests()
    {
        _parser = new CsvFileParser();
    }

    #region ParseFile Tests

    [Fact]
    public void ParseFile_WithValidCsvContent_ShouldReturnCorrectResult()
    {
        // Arrange
        string csvContent = "Name,Age,Email\nJohn <PERSON>,30,<EMAIL>\nJane <PERSON>,25,<EMAIL>";
        byte[] fileContent = Encoding.UTF8.GetBytes(csvContent);

        // Act
        FileParseResult result = _parser.ParseFile(fileContent);

        // Assert
        result.Should().NotBeNull();
        result.Headers.Should().HaveCount(3);
        result.Headers.Should().ContainInOrder("Name", "Age", "Email");
        result.Contents.Should().HaveCount(2);
        result.Count.Should().Be(2);

        // Verify first row
        result.Contents[0].Should().ContainKeys("Name", "Age", "Email");
        result.Contents[0]["Name"].Should().Be("John Doe");
        result.Contents[0]["Age"].Should().Be("30");
        result.Contents[0]["Email"].Should().Be("<EMAIL>");

        // Verify second row
        result.Contents[1]["Name"].Should().Be("Jane Smith");
        result.Contents[1]["Age"].Should().Be("25");
        result.Contents[1]["Email"].Should().Be("<EMAIL>");
    }

    [Fact]
    public void ParseFile_WithEmptyValues_ShouldReturnNullForEmptyValues()
    {
        // Arrange
        string csvContent = "Name,Age,Email\nJohn Doe,,<EMAIL>\n,25,";
        byte[] fileContent = Encoding.UTF8.GetBytes(csvContent);

        // Act
        FileParseResult result = _parser.ParseFile(fileContent);

        // Assert
        result.Contents[0]["Age"].Should().BeNull();
        result.Contents[1]["Name"].Should().BeNull();
        result.Contents[1]["Email"].Should().BeNull();
    }

    [Fact]
    public void ParseFile_WithWhitespaceValues_ShouldReturnNullForWhitespaceValues()
    {
        // Arrange
        string csvContent = "Name,Age,Email\nJohn Doe,  ,<EMAIL>\n   ,25,\t";
        byte[] fileContent = Encoding.UTF8.GetBytes(csvContent);

        // Act
        FileParseResult result = _parser.ParseFile(fileContent);

        // Assert
        result.Contents[0]["Age"].Should().BeNull();
        result.Contents[1]["Name"].Should().BeNull();
        result.Contents[1]["Email"].Should().BeNull();
    }

    [Fact]
    public void ParseFile_WithEmptyFile_ShouldThrowBadFileContentException()
    {
        // Arrange
        byte[] emptyContent = Array.Empty<byte>();

        // Act & Assert
        BadFileContentException exception = Assert.Throws<BadFileContentException>(() => _parser.ParseFile(emptyContent));
        exception.ErrorCode.Should().Be(BadFileContentErrorCode.EMPTY_FILE);
        exception.Message.Should().Be("File content is empty");
    }

    [Fact]
    public void ParseFile_WithNullContent_ShouldThrowBadFileContentException()
    {
        // Act & Assert
        BadFileContentException exception = Assert.Throws<BadFileContentException>(() => _parser.ParseFile(null!));
        exception.ErrorCode.Should().Be(BadFileContentErrorCode.EMPTY_FILE);
    }

    [Fact]
    public void ParseFile_WithHeaderOnly_ShouldReturnEmptyContents()
    {
        // Arrange
        string csvContent = "Name,Age,Email";
        byte[] fileContent = Encoding.UTF8.GetBytes(csvContent);

        // Act
        FileParseResult result = _parser.ParseFile(fileContent);

        // Assert
        result.Headers.Should().HaveCount(3);
        result.Contents.Should().BeEmpty();
        result.Count.Should().Be(0);
    }

    [Fact]
    public void ParseFile_WithMismatchedColumnCount_ShouldThrowBadFileContentException()
    {
        // Arrange
        string csvContent = "Name,Age,Email\nJohn Doe,30\nJane Smith,25,<EMAIL>,extra";
        byte[] fileContent = Encoding.UTF8.GetBytes(csvContent);

        // Act & Assert
        BadFileContentException exception = Assert.Throws<BadFileContentException>(() => _parser.ParseFile(fileContent));
        exception.ErrorCode.Should().Be(BadFileContentErrorCode.INVALID_ROW);
        exception.Message.Should().Contain("Invalid content at row index 1");
    }

    #endregion

    #region Caching Tests

    [Fact]
    public void ParseFile_WithSameContent_ShouldReturnCachedResult()
    {
        // Arrange
        string csvContent = "Name,Age\nJohn,30";
        byte[] fileContent = Encoding.UTF8.GetBytes(csvContent);

        // Act
        FileParseResult result1 = _parser.ParseFile(fileContent);
        FileParseResult result2 = _parser.ParseFile(fileContent);

        // Assert
        result1.Should().BeSameAs(result2); // Should return the exact same cached instance
    }

    [Fact]
    public void ParseFile_WithDifferentContent_ShouldNotReturnCachedResult()
    {
        // Arrange
        string csvContent1 = "Name,Age\nJohn,30";
        string csvContent2 = "Name,Age\nJane,25";
        byte[] fileContent1 = Encoding.UTF8.GetBytes(csvContent1);
        byte[] fileContent2 = Encoding.UTF8.GetBytes(csvContent2);

        // Act
        FileParseResult result1 = _parser.ParseFile(fileContent1);
        FileParseResult result2 = _parser.ParseFile(fileContent2);

        // Assert
        result1.Should().NotBeSameAs(result2);
        result1.Contents[0]["Name"].Should().Be("John");
        result2.Contents[0]["Name"].Should().Be("Jane");
    }

    #endregion

    #region HeadersSet Tests

    [Fact]
    public void ParseFile_ShouldInitializeHeadersSetWithCaseInsensitiveComparison()
    {
        // Arrange
        string csvContent = "Name,Age,Email\nJohn,30,<EMAIL>";
        byte[] fileContent = Encoding.UTF8.GetBytes(csvContent);

        // Act
        FileParseResult result = _parser.ParseFile(fileContent);

        // Assert
        result.HeadersSet.Should().HaveCount(3);
        result.HeadersSet.Contains("name").Should().BeTrue(); // Case insensitive
        result.HeadersSet.Contains("NAME").Should().BeTrue(); // Case insensitive
        result.HeadersSet.Contains("Age").Should().BeTrue();
        result.HeadersSet.Contains("NonExistent").Should().BeFalse();
    }

    #endregion

    #region Backward Compatibility Tests

    [Fact]
    public void GetHeaders_ShouldReturnSameAsParseFileHeaders()
    {
        // Arrange
        string csvContent = "Name,Age,Email\nJohn,30,<EMAIL>";
        byte[] fileContent = Encoding.UTF8.GetBytes(csvContent);

        // Act
        IEnumerable<string> headers = _parser.GetHeaders(fileContent);
        FileParseResult parseResult = _parser.ParseFile(fileContent);

        // Assert
        headers.Should().BeEquivalentTo(parseResult.Headers);
    }

    [Fact]
    public void GetContents_ShouldReturnSameAsParseFileContents()
    {
        // Arrange
        string csvContent = "Name,Age,Email\nJohn,30,<EMAIL>";
        byte[] fileContent = Encoding.UTF8.GetBytes(csvContent);

        // Act
        IList<IDictionary<string, string?>> contents = _parser.GetContents(fileContent);
        FileParseResult parseResult = _parser.ParseFile(fileContent);

        // Assert
        contents.Should().HaveCount(parseResult.Contents.Count);
        for (int i = 0; i < contents.Count; i++)
        {
            contents[i].Should().BeEquivalentTo(parseResult.Contents[i]);
        }
    }

    #endregion
}
