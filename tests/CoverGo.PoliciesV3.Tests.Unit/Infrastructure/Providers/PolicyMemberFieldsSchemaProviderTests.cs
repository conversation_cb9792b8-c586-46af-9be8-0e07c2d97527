using CoverGo.FeatureManagement;
using CoverGo.Multitenancy;
using CoverGo.PoliciesV3.Application.Common.Interfaces;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;
using CoverGo.PoliciesV3.Domain.CustomFields.Validation;
using CoverGo.PoliciesV3.Domain.Endorsements;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Infrastructure.Providers;
using Microsoft.Extensions.Logging;

namespace CoverGo.PoliciesV3.Tests.Unit.Infrastructure.Providers;

public class PolicyMemberFieldsSchemaProviderTests
{
    private readonly Mock<IPolicyMemberFieldsSchemaRepository> _schemaRepository;
    private readonly Mock<IMultiTenantFeatureManager> _featureManager;
    private readonly Mock<ILogger<PolicyMemberFieldsSchemaProvider>> _logger;
    private readonly PolicyMemberFieldsSchemaProvider _provider;
    private readonly IFixture _fixture;

    public PolicyMemberFieldsSchemaProviderTests()
    {
        _schemaRepository = new Mock<IPolicyMemberFieldsSchemaRepository>();
        _featureManager = new Mock<IMultiTenantFeatureManager>();
        _logger = new Mock<ILogger<PolicyMemberFieldsSchemaProvider>>();

        var tenantId = new TenantId("test-tenant");
        _provider = new PolicyMemberFieldsSchemaProvider(
            _schemaRepository.Object,
            _featureManager.Object,
            tenantId,
            _logger.Object);

        _fixture = new Fixture();
        _fixture.Customize<ProductId>(c => c.FromFactory(() => new ProductId("PLAN-001", "HEALTH", "1.0")));
        _fixture.Customize<EndorsementId>(c => c.FromFactory(() => EndorsementId.New));

        // Setup default feature flag behavior
        _featureManager.Setup(x => x.IsEnabled(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync(false);
    }

    [Fact]
    public async Task GetCustomFieldsSchema_ShouldReturnSchemaFromRepository()
    {
        // Arrange
        string contractHolderId = _fixture.Create<string>();
        ProductId productId = _fixture.Create<ProductId>();
        PolicyMemberFieldsSchema expectedSchema = CreateBaseSchema();

        _schemaRepository
            .Setup(x => x.GetCustomFieldsSchema(contractHolderId, productId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedSchema);

        // Act
        PolicyMemberFieldsSchema result = await _provider.GetCustomFieldsSchema(contractHolderId, productId, CancellationToken.None);

        // Assert
        result.Should().Be(expectedSchema);
        _schemaRepository.Verify(x => x.GetCustomFieldsSchema(contractHolderId, productId, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetMemberUploadSchema_ShouldApplyUploadSpecificProcessing()
    {
        // Arrange
        string contractHolderId = _fixture.Create<string>();
        ProductId productId = _fixture.Create<ProductId>();
        EndorsementId endorsementId = _fixture.Create<EndorsementId>();
        PolicyMemberFieldsSchema baseSchema = CreateBaseSchema();

        _schemaRepository
            .Setup(x => x.GetCustomFieldsSchema(contractHolderId, productId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(baseSchema);

        // Act
        PolicyMemberFieldsSchema result = await _provider.GetMemberUploadSchema(contractHolderId, productId, endorsementId, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.MemberFields.Should().NotBeEmpty();
        
        // Should contain system fields added by upload processing
        result.MemberFields.Should().Contain(f => f.Name == PolicyMemberUploadWellKnowFields.PlanIdField);
        result.MemberFields.Should().Contain(f => f.Name == PolicyMemberUploadWellKnowFields.ClassField);
        result.MemberFields.Should().Contain(f => f.Name == PolicyMemberUploadWellKnowFields.MemberIdField);
        result.MemberFields.Should().Contain(f => f.Name == PolicyMemberUploadWellKnowFields.DependentOfField);
        
        // Should contain effective date field when endorsementId is provided
        result.MemberFields.Should().Contain(f => f.Name == PolicyMemberUploadWellKnowFields.EffectiveDateField);
        
        // Should have identity validations
        result.OneOfValidations.Should().NotBeNull();
    }

    [Fact]
    public async Task GetMemberUploadSchema_WithoutEndorsement_ShouldNotIncludeEffectiveDate()
    {
        // Arrange
        string contractHolderId = _fixture.Create<string>();
        ProductId productId = _fixture.Create<ProductId>();
        PolicyMemberFieldsSchema baseSchema = CreateBaseSchema();

        _schemaRepository
            .Setup(x => x.GetCustomFieldsSchema(contractHolderId, productId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(baseSchema);

        // Act
        PolicyMemberFieldsSchema result = await _provider.GetMemberUploadSchema(contractHolderId, productId, null, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        
        // Should NOT contain effective date field when endorsementId is null
        result.MemberFields.Should().NotContain(f => f.Name == PolicyMemberUploadWellKnowFields.EffectiveDateField);
    }

    [Fact]
    public async Task GetMemberUploadSchema_WithRelationshipToEmployeeField_ShouldSetRequiredForDependent()
    {
        // Arrange
        string contractHolderId = _fixture.Create<string>();
        ProductId productId = _fixture.Create<ProductId>();
        PolicyMemberFieldsSchema baseSchema = CreateSchemaWithRelationshipField();

        _schemaRepository
            .Setup(x => x.GetCustomFieldsSchema(contractHolderId, productId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(baseSchema);

        // Act
        PolicyMemberFieldsSchema result = await _provider.GetMemberUploadSchema(contractHolderId, productId, null, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();

        PolicyMemberFieldDefinition? relationshipField = result.MemberFields.FirstOrDefault(f => f.Name == "relationshipToEmployee");
        relationshipField.Should().NotBeNull();
        relationshipField!.IsRequiredForDependent.Should().BeTrue();
    }

    [Fact]
    public async Task GetMemberUploadSchema_WithIdentityFields_ShouldCreateOneOfValidation()
    {
        // Arrange
        string contractHolderId = _fixture.Create<string>();
        ProductId productId = _fixture.Create<ProductId>();
        PolicyMemberFieldsSchema baseSchema = CreateSchemaWithIdentityFields();

        _schemaRepository
            .Setup(x => x.GetCustomFieldsSchema(contractHolderId, productId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(baseSchema);

        // Act
        PolicyMemberFieldsSchema result = await _provider.GetMemberUploadSchema(contractHolderId, productId, null, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.OneOfValidations.Should().NotBeNull();
        result.OneOfValidations.Should().HaveCount(1);

        CustomFieldOneOfValidation oneOfValidation = result.OneOfValidations!.First();
        oneOfValidation.Validations.Should().HaveCount(3); // passportNo, hkid, staffNo
        oneOfValidation.Validations.Should().Contain(v => v.Field.Name == "passportNo");
        oneOfValidation.Validations.Should().Contain(v => v.Field.Name == "hkid");
        oneOfValidation.Validations.Should().Contain(v => v.Field.Name == "staffNo");
    }

    #region Feature Flag Tests

    [Fact]
    public async Task GetMemberUploadSchema_WithFeatureFlagEnabled_ShouldIncludeEffectiveDateWithoutEndorsement()
    {
        // Arrange
        string contractHolderId = _fixture.Create<string>();
        ProductId productId = _fixture.Create<ProductId>();
        PolicyMemberFieldsSchema baseSchema = CreateBaseSchema();

        _schemaRepository
            .Setup(x => x.GetCustomFieldsSchema(contractHolderId, productId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(baseSchema);

        // Setup feature flag to return true
        _featureManager
            .Setup(x => x.IsEnabled("UseEffectiveDateInAddPolicyMember", "test-tenant"))
            .ReturnsAsync(true);

        // Act
        PolicyMemberFieldsSchema result = await _provider.GetMemberUploadSchema(contractHolderId, productId, null, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();

        // Should contain effective date field even without endorsement when feature flag is enabled
        result.MemberFields.Should().Contain(f => f.Name == PolicyMemberUploadWellKnowFields.EffectiveDateField);

        // Verify feature flag was called
        _featureManager.Verify(x => x.IsEnabled("UseEffectiveDateInAddPolicyMember", "test-tenant"), Times.Once);
    }

    [Fact]
    public async Task GetMemberUploadSchema_WithFeatureFlagDisabled_ShouldExcludeEffectiveDateWithoutEndorsement()
    {
        // Arrange
        string contractHolderId = _fixture.Create<string>();
        ProductId productId = _fixture.Create<ProductId>();
        PolicyMemberFieldsSchema baseSchema = CreateBaseSchema();

        _schemaRepository
            .Setup(x => x.GetCustomFieldsSchema(contractHolderId, productId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(baseSchema);

        // Setup feature flag to return false (default behavior)
        _featureManager
            .Setup(x => x.IsEnabled("UseEffectiveDateInAddPolicyMember", "test-tenant"))
            .ReturnsAsync(false);

        // Act
        PolicyMemberFieldsSchema result = await _provider.GetMemberUploadSchema(contractHolderId, productId, null, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();

        // Should NOT contain effective date field without endorsement when feature flag is disabled
        result.MemberFields.Should().NotContain(f => f.Name == PolicyMemberUploadWellKnowFields.EffectiveDateField);

        // Verify feature flag was called
        _featureManager.Verify(x => x.IsEnabled("UseEffectiveDateInAddPolicyMember", "test-tenant"), Times.Once);
    }

    [Fact]
    public async Task GetMemberUploadSchema_WithFeatureFlagEnabledAndEndorsement_ShouldIncludeEffectiveDate()
    {
        // Arrange
        string contractHolderId = _fixture.Create<string>();
        ProductId productId = _fixture.Create<ProductId>();
        EndorsementId endorsementId = _fixture.Create<EndorsementId>();
        PolicyMemberFieldsSchema baseSchema = CreateBaseSchema();

        _schemaRepository
            .Setup(x => x.GetCustomFieldsSchema(contractHolderId, productId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(baseSchema);

        // Setup feature flag to return true
        _featureManager
            .Setup(x => x.IsEnabled("UseEffectiveDateInAddPolicyMember", "test-tenant"))
            .ReturnsAsync(true);

        // Act
        PolicyMemberFieldsSchema result = await _provider.GetMemberUploadSchema(contractHolderId, productId, endorsementId, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();

        // Should contain effective date field when both feature flag is enabled AND endorsement is provided
        result.MemberFields.Should().Contain(f => f.Name == PolicyMemberUploadWellKnowFields.EffectiveDateField);

        // Verify the effective date field is required when endorsement is provided
        PolicyMemberFieldDefinition? effectiveDateField = result.MemberFields.FirstOrDefault(f => f.Name == PolicyMemberUploadWellKnowFields.EffectiveDateField);
        effectiveDateField.Should().NotBeNull();
        effectiveDateField!.IsRequired.Should().BeTrue();
        effectiveDateField.Type.Should().BeOfType<DateFieldType>();
    }

    [Fact]
    public async Task GetMemberUploadSchema_WithFeatureFlagDisabledButEndorsement_ShouldIncludeEffectiveDate()
    {
        // Arrange
        string contractHolderId = _fixture.Create<string>();
        ProductId productId = _fixture.Create<ProductId>();
        EndorsementId endorsementId = _fixture.Create<EndorsementId>();
        PolicyMemberFieldsSchema baseSchema = CreateBaseSchema();

        _schemaRepository
            .Setup(x => x.GetCustomFieldsSchema(contractHolderId, productId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(baseSchema);

        // Setup feature flag to return false
        _featureManager
            .Setup(x => x.IsEnabled("UseEffectiveDateInAddPolicyMember", "test-tenant"))
            .ReturnsAsync(false);

        // Act
        PolicyMemberFieldsSchema result = await _provider.GetMemberUploadSchema(contractHolderId, productId, endorsementId, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();

        // Should contain effective date field when endorsement is provided, regardless of feature flag
        result.MemberFields.Should().Contain(f => f.Name == PolicyMemberUploadWellKnowFields.EffectiveDateField);

        // Verify the effective date field is required when endorsement is provided
        PolicyMemberFieldDefinition? effectiveDateField = result.MemberFields.FirstOrDefault(f => f.Name == PolicyMemberUploadWellKnowFields.EffectiveDateField);
        effectiveDateField.Should().NotBeNull();
        effectiveDateField!.IsRequired.Should().BeTrue();
        effectiveDateField.Type.Should().BeOfType<DateFieldType>();
    }

    #endregion

    private static PolicyMemberFieldsSchema CreateBaseSchema()
    {
        var memberFields = new List<PolicyMemberFieldDefinition>
        {
            new()
            {
                Name = "firstName",
                Label = "First Name",
                Type = new StringFieldType { Options = null },
                IsRequired = true,
                IsUnique = false
            },
            new()
            {
                Name = "lastName",
                Label = "Last Name",
                Type = new StringFieldType { Options = null },
                IsRequired = true,
                IsUnique = false
            }
        };

        return new PolicyMemberFieldsSchema
        {
            MemberFields = memberFields,
            ProductFields = null,
            CensusFields = null,
            OneOfValidations = null
        };
    }

    private static PolicyMemberFieldsSchema CreateSchemaWithRelationshipField()
    {
        var memberFields = new List<PolicyMemberFieldDefinition>
        {
            new()
            {
                Name = "relationshipToEmployee",
                Label = "Relationship to Employee",
                Type = new StringFieldType { Options = null },
                IsRequired = false,
                IsRequiredForDependent = false,
                IsUnique = false
            }
        };

        return new PolicyMemberFieldsSchema
        {
            MemberFields = memberFields,
            ProductFields = null,
            CensusFields = null,
            OneOfValidations = null
        };
    }

    private static PolicyMemberFieldsSchema CreateSchemaWithIdentityFields()
    {
        var memberFields = new List<PolicyMemberFieldDefinition>
        {
            new()
            {
                Name = "passportNo",
                Label = "Passport Number",
                Type = new StringFieldType { Options = null },
                IsRequired = false,
                IsUnique = false
            },
            new()
            {
                Name = "hkid",
                Label = "Hong Kong ID",
                Type = new StringFieldType { Options = null },
                IsRequired = false,
                IsUnique = false
            },
            new()
            {
                Name = "staffNo",
                Label = "Staff Number",
                Type = new StringFieldType { Options = null },
                IsRequired = false,
                IsUnique = false
            }
        };

        return new PolicyMemberFieldsSchema
        {
            MemberFields = memberFields,
            ProductFields = null,
            CensusFields = null,
            OneOfValidations = null
        };
    }
}
