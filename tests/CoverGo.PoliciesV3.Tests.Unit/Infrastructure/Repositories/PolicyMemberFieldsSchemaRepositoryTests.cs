using CoverGo.PoliciesV3.Application.Services;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;
using CoverGo.PoliciesV3.Infrastructure.Repositories;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Linq;
using DomainProductId = CoverGo.PoliciesV3.Domain.Policies.ProductId;

namespace CoverGo.PoliciesV3.Tests.Unit.Infrastructure.Repositories;

public class PolicyMemberFieldsSchemaRepositoryTests
{
    private readonly Mock<ILogger<PolicyMemberFieldsSchemaRepository>> _logger;
    private readonly Mock<IUsersService> _usersService;
    private readonly Mock<ICasesService> _casesService;
    private readonly Mock<IProductService> _productService;
    private readonly PolicyMemberFieldsSchemaRepository _repository;
    private readonly Fixture _fixture;

    public PolicyMemberFieldsSchemaRepositoryTests()
    {
        _logger = new Mock<ILogger<PolicyMemberFieldsSchemaRepository>>();
        _usersService = new Mock<IUsersService>();
        _casesService = new Mock<ICasesService>();
        _productService = new Mock<IProductService>();

        // Setup mock data for CasesService to return firstName and lastName fields
        SetupCasesServiceMock();

        _repository = new PolicyMemberFieldsSchemaRepository(
            _usersService.Object,
            _casesService.Object,
            _productService.Object,
            _logger.Object);

        _fixture = new Fixture();
        _fixture.Customize<DomainProductId>(c => c.FromFactory(() => new DomainProductId("PLAN-001", "HEALTH", "1.0")));
    }

    private void SetupCasesServiceMock()
    {
        // Create mock JSON data that represents CasesDataSchema with firstName and lastName
        string mockCasesDataJson = """
        {
            "properties": {
                "firstName": {
                    "type": "string",
                    "meta": {
                        "label": "First Name",
                        "required": true
                    }
                },
                "lastName": {
                    "type": "string",
                    "meta": {
                        "label": "Last Name",
                        "required": true
                    }
                }
            }
        }
        """;

        var mockJToken = JToken.Parse(mockCasesDataJson);
        _casesService.Setup(x => x.GetMemberDataSchema(It.IsAny<CancellationToken>()))
                    .ReturnsAsync(mockJToken);
    }

    [Fact]
    public async Task GetCustomFieldsSchema_ShouldReturnBasicSchema()
    {
        // Arrange
        string contractHolderId = _fixture.Create<string>();
        DomainProductId productId = _fixture.Create<DomainProductId>();

        // Act
        PolicyMemberFieldsSchema result = await _repository.GetCustomFieldsSchema(contractHolderId, productId, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.MemberFields.Should().NotBeEmpty();
        result.MemberFields.Should().HaveCount(2); // firstName and lastName
        
        // Verify basic member fields
        result.MemberFields.Should().Contain(f => f.Name == "firstName" && f.Label == "First Name" && f.IsRequired);
        result.MemberFields.Should().Contain(f => f.Name == "lastName" && f.Label == "Last Name" && f.IsRequired);
        
        // Product and Census fields should be null (placeholder implementation)
        result.ProductFields.Should().BeNull();
        result.CensusFields.Should().BeNull();
        
        // OneOfValidations should be null (handled by provider)
        result.OneOfValidations.Should().BeNull();
    }

    [Fact]
    public async Task GetCustomFieldsSchema_WithNullContractHolderId_ShouldReturnSchema()
    {
        // Arrange
        string? contractHolderId = null;
        DomainProductId productId = _fixture.Create<DomainProductId>();

        // Act
        PolicyMemberFieldsSchema result = await _repository.GetCustomFieldsSchema(contractHolderId, productId, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.MemberFields.Should().NotBeEmpty();
    }

    [Fact]
    public async Task GetCustomFieldsSchema_ShouldLogInformationMessages()
    {
        // Arrange
        string contractHolderId = _fixture.Create<string>();
        DomainProductId productId = _fixture.Create<DomainProductId>();

        // Act
        await _repository.GetCustomFieldsSchema(contractHolderId, productId, CancellationToken.None);

        // Assert
        _logger.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Getting custom fields schema for")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);

        _logger.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Schema created with")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task GetCustomFieldsSchema_ShouldReturnFieldsWithCorrectTypes()
    {
        // Arrange
        string contractHolderId = _fixture.Create<string>();
        DomainProductId productId = _fixture.Create<DomainProductId>();

        // Act
        PolicyMemberFieldsSchema result = await _repository.GetCustomFieldsSchema(contractHolderId, productId, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();

        PolicyMemberFieldDefinition firstNameField = result.MemberFields.First(f => f.Name == "firstName");
        firstNameField.Type.Should().BeOfType<StringFieldType>();
        firstNameField.IsUnique.Should().BeFalse();

        PolicyMemberFieldDefinition lastNameField = result.MemberFields.First(f => f.Name == "lastName");
        lastNameField.Type.Should().BeOfType<StringFieldType>();
        lastNameField.IsUnique.Should().BeFalse();
    }

    [Fact]
    public async Task GetCustomFieldsSchema_WithCancellationToken_ShouldRespectCancellation()
    {
        // Arrange
        string contractHolderId = _fixture.Create<string>();
        DomainProductId productId = _fixture.Create<DomainProductId>();
        using var cts = new CancellationTokenSource();
        cts.Cancel();

        // Act & Assert
        await Assert.ThrowsAsync<OperationCanceledException>(
            () => _repository.GetCustomFieldsSchema(contractHolderId, productId, cts.Token));
    }

    [Fact]
    public async Task GetCustomFieldsSchema_ShouldCallExternalServices()
    {
        // Arrange
        string contractHolderId = _fixture.Create<string>();
        DomainProductId productId = _fixture.Create<DomainProductId>();

        // Act
        await _repository.GetCustomFieldsSchema(contractHolderId, productId, CancellationToken.None);

        // Assert - Verify that external service calls are made
        _casesService.Verify(x => x.GetMemberDataSchema(It.IsAny<CancellationToken>()), Times.Once);
        _usersService.Verify(x => x.GetCompanyContractHolderMembersFieldsById(contractHolderId, It.IsAny<CancellationToken>()), Times.Once);
        _productService.Verify(x => x.GetProductMemberSchema(It.IsAny<CoverGo.Products.Client.ProductId>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Theory]
    [InlineData("CONTRACT-001")]
    [InlineData("CONTRACT-999")]
    [InlineData(null)]
    public async Task GetCustomFieldsSchema_WithDifferentContractHolderIds_ShouldReturnConsistentSchema(string? contractHolderId)
    {
        // Arrange
        DomainProductId productId = _fixture.Create<DomainProductId>();

        // Act
        PolicyMemberFieldsSchema result = await _repository.GetCustomFieldsSchema(contractHolderId, productId, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.MemberFields.Should().HaveCount(2);
        result.MemberFields.Should().Contain(f => f.Name == "firstName");
        result.MemberFields.Should().Contain(f => f.Name == "lastName");
    }

    [Fact]
    public async Task GetCustomFieldsSchema_ShouldReturnNewInstanceEachTime()
    {
        // Arrange
        string contractHolderId = _fixture.Create<string>();
        DomainProductId productId = _fixture.Create<DomainProductId>();

        // Act
        PolicyMemberFieldsSchema result1 = await _repository.GetCustomFieldsSchema(contractHolderId, productId, CancellationToken.None);
        PolicyMemberFieldsSchema result2 = await _repository.GetCustomFieldsSchema(contractHolderId, productId, CancellationToken.None);

        // Assert
        result1.Should().NotBeSameAs(result2);
        result1.Should().BeEquivalentTo(result2);
    }

    [Fact]
    public void Constructor_WithNullLogger_ShouldThrowArgumentNullException()
    {
        // Arrange
        var usersService = new Mock<IUsersService>();
        var casesService = new Mock<ICasesService>();
        var productService = new Mock<IProductService>();

        // Act & Assert
        Assert.Throws<ArgumentNullException>(() => new PolicyMemberFieldsSchemaRepository(
            usersService.Object,
            casesService.Object,
            productService.Object,
            null!));
    }


}
