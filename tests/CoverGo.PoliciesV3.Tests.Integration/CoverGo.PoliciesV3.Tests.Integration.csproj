﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<IsPackable>false</IsPackable>
		<IsTestProject>true</IsTestProject>
	</PropertyGroup>

  <ItemGroup>
	  <PackageReference Include="CoverGo.Cases.Client.Rest" />
	  <PackageReference Include="CoverGo.FileSystem.Client" />
	  <PackageReference Include="CoverGo.Policies.Client" />
	  <PackageReference Include="CoverGo.Products.Client" />
	  <PackageReference Include="coverlet.collector" />
	  <PackageReference Include="JunitXml.TestLogger" />
	  <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" />
	  <PackageReference Include="Microsoft.AspNetCore.Hosting.Abstractions" />
	  <PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" />
	  <PackageReference Include="Microsoft.NET.Test.Sdk" />
	  <PackageReference Include="xunit" />
	  <PackageReference Include="xunit.runner.visualstudio" />
	  <PackageReference Include="Microsoft.EntityFrameworkCore.Relational" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\CoverGo.PoliciesV3.Api\CoverGo.PoliciesV3.Api.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Using Include="Xunit" />
  </ItemGroup>

</Project>
