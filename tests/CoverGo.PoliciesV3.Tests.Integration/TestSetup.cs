﻿using CoverGo.Cases.Client.Rest;
using CoverGo.FileSystem.Client;
using CoverGo.Multitenancy;
using CoverGo.Policies.Client;
using CoverGo.Products.Client;
using CoverGo.Users.Client;
using GraphQL.Client.Http;
using GraphQL.Client.Serializer.Newtonsoft;
using IdentityModel.Client;

namespace CoverGo.PoliciesV3.Tests.Integration;

internal static class TestSetup
{
    public static readonly Lazy<TestConfig> Config = new(() => TestConfig.Load());

    public static PoliciesClient BuildPoliciesHttpClient(string accessToken)
    {
        var httpClient = new HttpClient { BaseAddress = new Uri(Config.Value.PoliciesUrl) };
        if (!string.IsNullOrEmpty(accessToken))
            httpClient.DefaultRequestHeaders.Authorization = new("Bearer", accessToken);
        return new PoliciesClient(httpClient);
    }

    public static FileSystemClient BuildFileSystemHttpClient() => new(new HttpClient { BaseAddress = new Uri(Config.Value.FileSystemUrl) });

    public static ProductsClient BuildProductsHttpClient() => new(new HttpClient { BaseAddress = new Uri(Config.Value.ProductsUrl) });

    public static ICasesRestClient BuildCasesClient() => new CasesRestClient(new HttpClient { BaseAddress = new Uri(Config.Value.CasesUrl) });

    public static UsersClient BuildUsersClient() => new(new HttpClient { BaseAddress = new Uri(Config.Value.UsersUrl) });

    public static async Task<GraphQLHttpClient> BuildPoliciesGraphQlClient(TenantId? tenantId = null)
    {
        var client = new GraphQLHttpClient(new Uri($"{Config.Value.PoliciesUrl}/graphql"), new NewtonsoftJsonSerializer());
        string accessToken = await GetAccessToken(tenantId);
        client.HttpClient.DefaultRequestHeaders.Authorization = new("Bearer", accessToken);
        return client;
    }

    public static async Task<GraphQLHttpClient> BuildPoliciesGraphQlClient(HttpClient httpClient, TenantId? tenantId = null)
    {
        var client = new GraphQLHttpClient(new GraphQLHttpClientOptions(), new NewtonsoftJsonSerializer(), httpClient);
        string accessToken = await GetAccessToken(tenantId);
        client.HttpClient.DefaultRequestHeaders.Authorization = new("Bearer", accessToken);
        return client;
    }

    public static async Task<string> GetAccessToken(TenantId? tenantId = null, string? clientId = null, string? userName = null, string? password = null)
    {
        tenantId ??= new TenantId("covergo");
        var client = new HttpClient();

        TokenResponse response = await client.RequestPasswordTokenAsync(new()
        {
            Address = $"{Config.Value.AuthUrl}/{tenantId.Value}/connect/token",
            ClientId = clientId ?? UserCredentials.Admin.ClientId,
            UserName = userName ?? UserCredentials.Admin.UserName,
            Password = password ?? UserCredentials.Admin.Password,
            Scope = "custom_profile offline_access",
        });

        return response.AccessToken;
    }
}
