﻿using CoverGo.GraphQL.Client;

namespace CoverGo.PoliciesV3.Tests.Integration;

internal class TestBase : IClassFixture<CustomWebApplicationFactory>, IAsyncLifetime
{
    protected readonly CustomWebApplicationFactory _factory;
    public CoverGoGraphQlClient Gateway { get; private set; } = default!;
    public string TenantId { get; } = UserCredentials.Admin.TenantId;

    protected TestBase(CustomWebApplicationFactory factory)
    {
        _factory = factory;
    }

    async Task IAsyncLifetime.InitializeAsync()
    {
        HttpClient httpClient = _factory.CreateClient(new()
        {
            BaseAddress = new Uri($"{TestSetup.Config.Value.PoliciesUrl}/graphql"),
        });
        global::GraphQL.Client.Http.GraphQLHttpClient graphqlClient = await TestSetup.BuildPoliciesGraphQlClient(httpClient, new(TenantId));
        Gateway = new CoverGoGraphQlClient();
        await Gateway.Authorize();
    }

    Task IAsyncLifetime.DisposeAsync()
    {
        Gateway.Dispose();
        return Task.CompletedTask;
    }
}