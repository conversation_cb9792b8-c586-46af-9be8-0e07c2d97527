using CoverGo.PoliciesV3.Domain.Common;

namespace CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Exceptions;

/// <summary>
/// Exception thrown when an uploaded file cannot be found
/// </summary>
public class UploadFileNotFoundException : DomainException
{
    public UploadFileNotFoundException(string policyId, string path)
        : base($"Upload file path {path} of policy {policyId} not found")
    {
        PolicyId = policyId;
        Path = path;
    }

    public UploadFileNotFoundException(string policyId, string path, Exception innerException)
        : base($"Upload file path {path} of policy {policyId} not found", innerException)
    {
        PolicyId = policyId;
        Path = path;
    }

    public override string Code => "UPLOAD_FILE_NOT_FOUND";

    public string PolicyId { get; }
    public string Path { get; }
}
