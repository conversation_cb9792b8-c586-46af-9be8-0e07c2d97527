using CoverGo.PoliciesV3.Domain.Common;

namespace CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Exceptions;

/// <summary>
/// Exception thrown when file content is invalid or cannot be parsed
/// </summary>
public class BadFileContentException : DomainException
{
    public BadFileContentException(string errorCode, string message) : base(message)
    {
        ErrorCode = errorCode;
    }

    public BadFileContentException(string errorCode, string message, Exception innerException) : base(message, innerException)
    {
        ErrorCode = errorCode;
    }

    /// <summary>
    /// Constructor for multiple validation errors
    /// </summary>
    public BadFileContentException(IEnumerable<(string errorCode, string message)> errors)
        : base(string.Join("; ", errors.Select(e => e.message)))
    {
        ErrorCode = errors.FirstOrDefault().errorCode ?? BadFileContentErrorCode.INVALID_ROW;
        Errors = [.. errors];
    }

    public override string Code => ErrorCode;

    /// <summary>
    /// The specific error code for this file content error
    /// </summary>
    public string ErrorCode { get; }

    /// <summary>
    /// Collection of all validation errors
    /// </summary>
    public IReadOnlyList<(string errorCode, string message)> Errors { get; } = new List<(string, string)>();
}


