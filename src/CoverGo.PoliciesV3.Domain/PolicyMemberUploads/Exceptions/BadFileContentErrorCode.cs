namespace CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Exceptions;

/// <summary>
/// Error codes for file content validation
/// </summary>
public static class BadFileContentErrorCode
{
    public const string EMPTY_FILE = "EMPTY_FILE";
    public const string INVALID_ROW = "INVALID_ROW";
    public const string INVALID_XLSX_FILE = "INVALID_XLSX_FILE";
    public const string UNSUPPORTED_FILE_TYPE = "UNSUPPORTED_FILE_TYPE";
    public const string NO_COLUMN = "NO_COLUMN";
    public const string NO_MEMBER = "NO_MEMBER";
    public const string MISSING_MANDATORY_COLUMNS = "MISSING_MANDATORY_COLUMNS";
    public const string MISSING_ONE_OF_MANDATORY_COLUMNS = "MISSING_ONE_OF_MANDATORY_COLUMNS";
    public const string EXTRA_COLUMN = "EXTRA_COLUMN";
}
