namespace CoverGo.PoliciesV3.Domain.PolicyMemberUploads;

/// <summary>
/// Constants for well-known policy member upload field names
/// </summary>
public static class PolicyMemberUploadWellKnowFields
{
    public const string PlanIdField = "planId";
    public const string ClassField = "class";
    public const string EffectiveDateField = "effectiveDate";
    public const string MemberIdField = "memberId";
    public const string DependentOfField = "dependentOf";

    public static string[] All { get; } = [PlanIdField, ClassField, EffectiveDateField, MemberIdField, DependentOfField];
}
