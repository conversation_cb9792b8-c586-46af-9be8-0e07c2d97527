using CoverGo.PoliciesV3.Domain.Common;
using CoverGo.PoliciesV3.Domain.PolicyMembers;

namespace CoverGo.PoliciesV3.Domain.PolicyMemberUploads;

/// <summary>
/// Represents the outcome of attempting to import a specific row from the uploaded member file
/// </summary>
public class PolicyMemberUploadImportedResult
{
    #region Constructors

    private PolicyMemberUploadImportedResult(Guid id)
    {
        Id = id;
    }

    public PolicyMemberUploadImportedResult() : this(Guid.CreateVersion7())
    {
    }

    #endregion

    #region Fields / Properties

    /// <summary>
    /// Primary key
    /// </summary>
    public Guid Id { get; private set; }

    /// <summary>
    /// Foreign key to the PolicyMemberUpload
    /// </summary>
    public PolicyMemberUploadId PolicyMemberUploadId { get; private set; } = PolicyMemberUploadId.Empty;

    /// <summary>
    /// The row index in the uploaded file
    /// </summary>
    public int RowIndex { get; private set; }

    /// <summary>
    /// Whether the import was successful
    /// </summary>
    public bool Success { get; private set; }

    /// <summary>
    /// The ID of the created PolicyMember if import was successful
    /// </summary>
    public PolicyMemberId? PolicyMemberId { get; private set; }

    /// <summary>
    /// JSON representation of the importing error if import failed
    /// </summary>
    public string? ImportingErrorJson { get; private set; }

    #endregion

    #region Navigation Properties

    /// <summary>
    /// Navigation property to the parent PolicyMemberUpload
    /// </summary>
    public virtual PolicyMemberUpload PolicyMemberUpload { get; set; } = null!;

    #endregion

    #region Factory Methods

    /// <summary>
    /// Creates a successful import result
    /// </summary>
    /// <param name="policyMemberUploadId">The upload this result belongs to</param>
    /// <param name="rowIndex">The row index that was imported</param>
    /// <param name="policyMemberId">The ID of the created PolicyMember</param>
    /// <returns>A new successful PolicyMemberUploadImportedResult instance</returns>
    public static PolicyMemberUploadImportedResult CreateSuccess(
        PolicyMemberUploadId policyMemberUploadId,
        int rowIndex,
        PolicyMemberId policyMemberId)
    {
        var result = new PolicyMemberUploadImportedResult
        {
            PolicyMemberUploadId = policyMemberUploadId,
            RowIndex = rowIndex,
            Success = true,
            PolicyMemberId = policyMemberId,
            ImportingErrorJson = null
        };

        return result;
    }

    /// <summary>
    /// Creates a failed import result
    /// </summary>
    /// <param name="policyMemberUploadId">The upload this result belongs to</param>
    /// <param name="rowIndex">The row index that failed to import</param>
    /// <param name="error">The error that occurred during import</param>
    /// <param name="policyMemberId">Optional ID of the PolicyMember if it was created before the failure occurred</param>
    /// <returns>A new failed PolicyMemberUploadImportedResult instance</returns>
    public static PolicyMemberUploadImportedResult CreateFailure(
        PolicyMemberUploadId policyMemberUploadId,
        int rowIndex,
        IUserError error,
        PolicyMemberId? policyMemberId = null)
    {
        var result = new PolicyMemberUploadImportedResult
        {
            PolicyMemberUploadId = policyMemberUploadId,
            RowIndex = rowIndex,
            Success = false,
            PolicyMemberId = policyMemberId,
            ImportingErrorJson = System.Text.Json.JsonSerializer.Serialize(new { error.Code, error.Message })
        };

        return result;
    }

    #endregion
}