using CoverGo.PoliciesV3.Domain.CustomFields;

namespace CoverGo.PoliciesV3.Domain.PolicyMemberUploads;

/// <summary>
/// Represents the complete schema for policy member upload files with additional processing
/// This extends the base PolicyMemberFieldsSchema with upload-specific functionality
/// </summary>
public record PolicyMemberUploadFileSchema : PolicyMemberFieldsSchema
{
    // This class extends the base PolicyMemberFieldsSchema with upload-specific functionality
    // Additional properties and methods can be added here as needed for upload processing
}
