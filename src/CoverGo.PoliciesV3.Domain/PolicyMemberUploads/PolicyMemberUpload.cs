using CoverGo.BuildingBlocks.Domain.Core.Entities;
using CoverGo.PoliciesV3.Domain.Common;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMembers;

namespace CoverGo.PoliciesV3.Domain.PolicyMemberUploads;

/// <summary>
/// Represents a batch upload of policy members, tracking the file, member counts, and the status of the upload process
/// </summary>
public class PolicyMemberUpload : AggregateRootBase<PolicyMemberUploadId>
{
    #region Constructors

    private PolicyMemberUpload(PolicyMemberUploadId id) : base(id)
    {
        CreatedAt = DateTimeOffset.UtcNow;
    }

    public PolicyMemberUpload() : this(PolicyMemberUploadId.New)
    {
    }

    #endregion

    #region Fields / Properties

    // Entity Relationships
    /// <summary>
    /// Foreign key to the Policy
    /// </summary>
    public PolicyId PolicyId { get; private set; } = PolicyId.Empty;

    /// <summary>
    /// Optional foreign key to an Endorsement
    /// </summary>
    public Guid? EndorsementId { get; private set; }

    // File Information
    /// <summary>
    /// Path to the uploaded file
    /// </summary>
    public string Path { get; private set; } = string.Empty;

    // Member Counts
    /// <summary>
    /// Total number of members in the uploaded file
    /// </summary>
    public int MembersCount { get; private set; }

    /// <summary>
    /// Number of valid members after validation
    /// </summary>
    public int? ValidMembersCount { get; private set; }

    /// <summary>
    /// Number of invalid members after validation
    /// </summary>
    public int? InvalidMembersCount { get; private set; }

    // Status Information
    /// <summary>
    /// Current status of the upload process
    /// </summary>
    public PolicyMemberUploadStatus Status { get; private set; } = PolicyMemberUploadStatus.REGISTERED;

    // Timestamps
    /// <summary>
    /// When the upload was created
    /// </summary>
    public DateTimeOffset CreatedAt { get; private set; }

    /// <summary>
    /// When the upload was last modified
    /// </summary>
    public DateTimeOffset? LastModifiedAt { get; private set; }

    // Collections
    /// <summary>
    /// Collection of validation errors
    /// </summary>
    public virtual ICollection<PolicyMemberUploadValidationError> ValidationErrors { get; set; } = [];

    /// <summary>
    /// Collection of import results
    /// </summary>
    public virtual ICollection<PolicyMemberUploadImportedResult> ImportedResults { get; set; } = [];

    #endregion

    #region Factory Methods

    /// <summary>
    /// Creates a new policy member upload
    /// </summary>
    /// <param name="policyId">The policy this upload belongs to</param>
    /// <param name="path">Path to the uploaded file</param>
    /// <param name="membersCount">Total number of members in the file</param>
    /// <param name="endorsementId">Optional endorsement ID</param>
    /// <returns>A new PolicyMemberUpload instance</returns>
    public static PolicyMemberUpload Create(
        PolicyId policyId,
        string path,
        int membersCount,
        Guid? endorsementId = null)
    {
        var upload = new PolicyMemberUpload
        {
            PolicyId = policyId,
            Path = path,
            MembersCount = membersCount,
            EndorsementId = endorsementId,
            Status = PolicyMemberUploadStatus.REGISTERED
        };
        return upload;
    }

    #endregion

    #region Validation Process

    /// <summary>
    /// Starts the validation process
    /// </summary>
    public void StartValidating()
    {
        EnsureCanStartValidation();
        Status = PolicyMemberUploadStatus.VALIDATING;
        LastModifiedAt = DateTimeOffset.UtcNow;
    }

    /// <summary>
    /// Attempts to finish validation with the provided counts
    /// </summary>
    /// <param name="validCount">Number of valid members</param>
    /// <param name="invalidCount">Number of invalid members</param>
    /// <returns>True if validation finished successfully, false otherwise</returns>
    public bool TryFinishValidation(int validCount, int invalidCount)
    {
        if (Status != PolicyMemberUploadStatus.VALIDATING)
            return false;

        ValidMembersCount = validCount;
        InvalidMembersCount = invalidCount;

        Status = invalidCount > 0
            ? PolicyMemberUploadStatus.VALIDATION_FAILED
            : PolicyMemberUploadStatus.VALIDATION_COMPLETED;

        LastModifiedAt = DateTimeOffset.UtcNow;
        return true;
    }

    /// <summary>
    /// Adds a validation error to the upload
    /// </summary>
    /// <param name="rowIndex">Row index where the error occurred</param>
    /// <param name="code">Error code</param>
    /// <param name="message">Error message</param>
    public void AddValidationError(int rowIndex, string code, string message)
    {
        var error = PolicyMemberUploadValidationError.Create(Id, rowIndex, code, message);
        ValidationErrors.Add(error);
    }

    #endregion

    #region Import Process

    /// <summary>
    /// Starts the import process
    /// </summary>
    public void StartImporting()
    {
        EnsureCanImport();
        Status = PolicyMemberUploadStatus.IMPORTING;
        LastModifiedAt = DateTimeOffset.UtcNow;
    }

    /// <summary>
    /// Completes the import process successfully
    /// </summary>
    public void CompleteImport()
    {
        if (Status != PolicyMemberUploadStatus.IMPORTING)
            throw new InvalidOperationException($"Cannot complete import when status is {Status.Value}. Status must be {PolicyMemberUploadStatus.IMPORTING.Value}.");

        Status = PolicyMemberUploadStatus.IMPORT_COMPLETED;
        LastModifiedAt = DateTimeOffset.UtcNow;
    }

    /// <summary>
    /// Marks the import process as failed
    /// </summary>
    public void FailImport()
    {
        if (Status != PolicyMemberUploadStatus.IMPORTING)
            throw new InvalidOperationException($"Cannot fail import when status is {Status.Value}. Status must be {PolicyMemberUploadStatus.IMPORTING.Value}.");

        Status = PolicyMemberUploadStatus.IMPORT_FAILED;
        LastModifiedAt = DateTimeOffset.UtcNow;
    }

    /// <summary>
    /// Adds a successful import result
    /// </summary>
    /// <param name="rowIndex">Row index that was imported</param>
    /// <param name="policyMemberId">ID of the created policy member</param>
    public void AddSuccessfulImportResult(int rowIndex, PolicyMemberId policyMemberId)
    {
        var result = PolicyMemberUploadImportedResult.CreateSuccess(Id, rowIndex, policyMemberId);
        ImportedResults.Add(result);
    }

    /// <summary>
    /// Adds a failed import result
    /// </summary>
    /// <param name="rowIndex">Row index that failed to import</param>
    /// <param name="error">The error that occurred</param>
    /// <param name="policyMemberId">Optional ID of the PolicyMember if it was created before the failure occurred</param>
    public void AddFailedImportResult(int rowIndex, IUserError error, PolicyMemberId? policyMemberId = null)
    {
        var result = PolicyMemberUploadImportedResult.CreateFailure(Id, rowIndex, error, policyMemberId);
        ImportedResults.Add(result);
    }

    #endregion

    #region Status Management

    /// <summary>
    /// Marks the entire upload as failed
    /// </summary>
    public void FailUpload()
    {
        Status = PolicyMemberUploadStatus.FAILED;
        LastModifiedAt = DateTimeOffset.UtcNow;
    }

    #endregion

    #region Private Methods

    private void EnsureCanStartValidation()
    {
        if (Status != PolicyMemberUploadStatus.REGISTERED)
            throw new InvalidOperationException($"Cannot start validation when status is {Status.Value}. Status must be {PolicyMemberUploadStatus.REGISTERED.Value}.");
    }

    /// <summary>
    /// Ensures the upload can proceed to import
    /// </summary>
    /// <exception cref="InvalidOperationException">Thrown when import cannot proceed</exception>
    private void EnsureCanImport()
    {
        if (Status != PolicyMemberUploadStatus.VALIDATION_COMPLETED)
            throw new InvalidOperationException($"Cannot import when status is {Status.Value}. Status must be {PolicyMemberUploadStatus.VALIDATION_COMPLETED.Value}.");

        if (ValidMembersCount == 0)
            throw new InvalidOperationException("Cannot import when there are no valid members.");
    }

    #endregion
}