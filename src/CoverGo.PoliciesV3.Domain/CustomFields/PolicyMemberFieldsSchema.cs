using CoverGo.PoliciesV3.Domain.CustomFields.Validation;

namespace CoverGo.PoliciesV3.Domain.CustomFields;

/// <summary>
/// Represents the complete schema for policy member fields validation
/// </summary>
public record PolicyMemberFieldsSchema
{
    private readonly IDictionary<string, PolicyMemberFieldDefinition> _systemMemberFields = new Dictionary<string, PolicyMemberFieldDefinition>();
    private readonly IDictionary<string, PolicyMemberFieldDefinition> _customMemberFields = new Dictionary<string, PolicyMemberFieldDefinition>();
    private readonly IDictionary<string, PolicyMemberFieldDefinition>? _productFields;
    private readonly IDictionary<string, PolicyMemberFieldDefinition>? _censusFields;

    /// <summary>
    /// Gets field sources in priority order: system > custom > product > census
    /// </summary>
    private List<IDictionary<string, PolicyMemberFieldDefinition>?> GetFieldSourcesInPriority() =>
        [_systemMemberFields, _customMemberFields, _productFields, _censusFields];

    /// <summary>
    /// System member fields (well-known fields)
    /// </summary>
    public IReadOnlyList<PolicyMemberFieldDefinition> MemberSystemFields =>
        [.. _systemMemberFields.Values];

    /// <summary>
    /// Custom member fields (user-defined fields)
    /// </summary>
    public IReadOnlyList<PolicyMemberFieldDefinition> MemberCustomFields =>
        [.. _customMemberFields.Values];

    /// <summary>
    /// All member fields (system + custom)
    /// </summary>
    public required IReadOnlyList<PolicyMemberFieldDefinition> MemberFields
    {
        get => [.. _customMemberFields.Values.ToList().Union([.. _systemMemberFields.Values])];
        init
        {
            // Split fields into system and custom based on well-known field names
            foreach (PolicyMemberFieldDefinition field in value)
            {
                _customMemberFields[field.Name] = field;
            }
        }
    }

    /// <summary>
    /// Product-specific fields
    /// </summary>
    public IReadOnlyList<PolicyMemberFieldDefinition>? ProductFields
    {
        get => _productFields?.Values.ToList();
        init => _productFields = value?.ToDictionary(field => field.Name, field => field);
    }

    /// <summary>
    /// Census-specific fields
    /// </summary>
    public IReadOnlyList<PolicyMemberFieldDefinition>? CensusFields
    {
        get => _censusFields?.Values.ToList();
        init => _censusFields = value?.ToDictionary(field => field.Name, field => field);
    }

    /// <summary>
    /// One-of validation rules for conditional field requirements
    /// </summary>
    public IReadOnlyList<CustomFieldOneOfValidation>? OneOfValidations { get; init; }

    /// <summary>
    /// All fields with priority: member > product > census
    /// </summary>
    public IReadOnlyList<PolicyMemberFieldDefinition> Fields
    {
        get
        {
            List<IDictionary<string, PolicyMemberFieldDefinition>?> sources = GetFieldSourcesInPriority();
            return [.. sources
                .Aggregate(
                    Enumerable.Empty<KeyValuePair<string, PolicyMemberFieldDefinition>>(),
                    (result, nextItem) =>
                        nextItem != null
                            ? result.UnionBy(
                                [.. nextItem],
                                kvp => kvp.Key)
                            : result)
                .Select(kvp => kvp.Value)];
        }
    }

    /// <summary>
    /// Gets a field by name with priority: system > member > product > census
    /// </summary>
    public PolicyMemberFieldDefinition? GetField(string fieldName)
    {
        List<IDictionary<string, PolicyMemberFieldDefinition>?> sources = GetFieldSourcesInPriority();
        foreach (IDictionary<string, PolicyMemberFieldDefinition>? source in sources)
        {
            if (source is not null && source.TryGetValue(fieldName, out PolicyMemberFieldDefinition? fieldDefinition))
            {
                return fieldDefinition;
            }
        }
        return null;
    }
}
