using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.Interfaces;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.ValidationErrors;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.Values;

namespace CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;

/// <summary>
/// Represents a number field type
/// </summary>
public sealed record NumberFieldType : CustomFieldTypeBase, IOptionsFieldType<NumberOption>
{
    public List<NumberOption>? Options { get; init; }

    public override List<IFieldValidationError>? ValidateField(object? fieldValue, PolicyMemberFieldDefinition field, IDictionary<string, object?>? otherFields = null)
    {
        if (fieldValue == null)
            return FieldValidationErrorFactory.CreateNoErrors();

        NumberValue? numberValue = NumberValue.CreateNullable(fieldValue);
        if (numberValue is null)
            return FieldValidationErrorFactory.CreateSingleError(FieldValidationErrorFactory.CreateInvalidNumberError(field));

        // Check options if they exist
        if (Options != null && !Options.Select(x => x.Value).Contains(numberValue.Value))
        {
            var stringOptions = Options.Select(o => new StringOption { Value = o.Value.ToString()!, Label = o.Label }).ToList();
            return FieldValidationErrorFactory.CreateSingleError(FieldValidationErrorFactory.CreateInvalidStringOptionError(field, stringOptions));
        }

        return FieldValidationErrorFactory.CreateNoErrors();
    }

    protected override List<IFieldValidationError>? TryParseFieldSpecific(object? fieldValue, PolicyMemberFieldDefinition field, out object? parsedFieldValue, IDictionary<string, object?>? otherFields = null)
    {
        parsedFieldValue = fieldValue is not null
            ? NumberValue.Create(fieldValue).Value
            : null;
        return null;
    }
}

/// <summary>
/// Represents a numeric option for number field types
/// </summary>
public record NumberOption
{
    public required object Value { get; init; }
    public string? Label { get; init; }
}
