using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.Constants;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.Extensions;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.ValidationErrors;

namespace CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;

/// <summary>
/// Represents a date field type with validation support
/// </summary>
public sealed record DateFieldType : CustomFieldTypeBase
{
    public override List<IFieldValidationError>? ValidateField(object? fieldValue, PolicyMemberFieldDefinition field, IDictionary<string, object?>? otherFields = null)
    {
        if (fieldValue is null or DateTime or DateOnly)
            return FieldValidationErrorFactory.CreateNoErrors();

        if (fieldValue is string strValue)
        {
            if (!strValue.TryParseDateTime())
                return FieldValidationErrorFactory.CreateSingleError(FieldValidationErrorFactory.CreateInvalidDateError(field));
        }
        else
        {
            return FieldValidationErrorFactory.CreateSingleError(FieldValidationErrorFactory.CreateInvalidTypeError(field));
        }

        return FieldValidationErrorFactory.CreateNoErrors();
    }

    protected override List<IFieldValidationError>? TryParseFieldSpecific(object? fieldValue, PolicyMemberFieldDefinition field, out object? parsedFieldValue, IDictionary<string, object?>? otherFields = null)
    {
        parsedFieldValue = fieldValue;

        if (fieldValue == null)
            return FieldValidationErrorFactory.CreateNoErrors();

        DateOnly? parsedValue = fieldValue switch
        {
            string strValue => DateOnly.FromDateTime(strValue.ParseDateTime()),
            DateTime dateTime => DateOnly.FromDateTime(dateTime),
            DateOnly dateOnly => dateOnly,
            _ => null
        };

        parsedFieldValue = parsedValue;

        // Apply age validation rules if present
        if (parsedValue.HasValue)
        {
            var validationErrors = ValidateAgeRules(parsedValue.Value, field, otherFields);
            if (validationErrors != null)
                return validationErrors;
        }

        return FieldValidationErrorFactory.CreateNoErrors();
    }

    private List<IFieldValidationError>? ValidateAgeRules(DateOnly dateValue, PolicyMemberFieldDefinition field, IDictionary<string, object?>? otherFields)
    {
        foreach (ValidationInfo validationInfo in ValidationInfo)
        {
            var error = validationInfo.Name switch
            {
                FieldValidationConstants.ValidationRules.MinEmployeeAge => ValidateMinEmployeeAge(dateValue, validationInfo, field, otherFields),
                FieldValidationConstants.ValidationRules.MaxEmployeeAge => ValidateMaxEmployeeAge(dateValue, validationInfo, field, otherFields),
                FieldValidationConstants.ValidationRules.MinChildDays => ValidateMinChildDays(dateValue, validationInfo, field, otherFields),
                FieldValidationConstants.ValidationRules.MinSpouseAge => ValidateMinSpouseAge(dateValue, validationInfo, field, otherFields),
                _ => null
            };

            if (error != null)
                return FieldValidationErrorFactory.CreateSingleError(error);
        }

        return FieldValidationErrorFactory.CreateNoErrors();
    }

    private static IFieldValidationError? ValidateMinEmployeeAge(DateOnly dateValue, ValidationInfo validationInfo, PolicyMemberFieldDefinition field, IDictionary<string, object?>? otherFields)
    {
        if (otherFields?.TryGetValue("memberType", out var memberTypeValue) != true || memberTypeValue?.ToString() != FieldValidationConstants.MemberTypes.Employee)
            return null;

        if (!int.TryParse(validationInfo.Arguments.FirstOrDefault(), out int minAge))
            return null;

        int age = CalculateAge(dateValue);
        return age < minAge
            ? new FieldMinAgeException(minAge) { PropertyPath = field.Name, PropertyLabel = field.Label }
            : null;
    }

    private static IFieldValidationError? ValidateMaxEmployeeAge(DateOnly dateValue, ValidationInfo validationInfo, PolicyMemberFieldDefinition field, IDictionary<string, object?>? otherFields)
    {
        if (otherFields?.TryGetValue("memberType", out var memberTypeValue) != true || memberTypeValue?.ToString() != FieldValidationConstants.MemberTypes.Employee)
            return null;

        if (!int.TryParse(validationInfo.Arguments.FirstOrDefault(), out int maxAge))
            return null;

        int age = CalculateAge(dateValue);
        return age > maxAge
            ? new FieldMaxAgeException(maxAge) { PropertyPath = field.Name, PropertyLabel = field.Label }
            : null;
    }

    private static IFieldValidationError? ValidateMinChildDays(DateOnly dateValue, ValidationInfo validationInfo, PolicyMemberFieldDefinition field, IDictionary<string, object?>? otherFields)
    {
        if (otherFields?.TryGetValue("relationshipToEmployee", out var relationshipValue) != true || relationshipValue?.ToString() != FieldValidationConstants.MemberTypes.Child)
            return null;

        if (!int.TryParse(validationInfo.Arguments.FirstOrDefault(), out int minDays))
            return null;

        int daysSinceBirth = (int)(DateTime.Now - dateValue.ToDateTime(new TimeOnly())).TotalDays;
        return daysSinceBirth < minDays
            ? new FieldMinChildDaysException(minDays) { PropertyPath = field.Name, PropertyLabel = field.Label }
            : null;
    }

    private static IFieldValidationError? ValidateMinSpouseAge(DateOnly dateValue, ValidationInfo validationInfo, PolicyMemberFieldDefinition field, IDictionary<string, object?>? otherFields)
    {
        if (otherFields?.TryGetValue("relationshipToEmployee", out var relationshipValue) != true || relationshipValue?.ToString() != FieldValidationConstants.MemberTypes.Spouse)
            return null;

        if (!int.TryParse(validationInfo.Arguments.FirstOrDefault(), out int minAge))
            return null;

        int age = CalculateAge(dateValue);
        return age < minAge
            ? new FieldMinSpouseAgeException(minAge) { PropertyPath = field.Name, PropertyLabel = field.Label }
            : null;
    }

    private static int CalculateAge(DateOnly birthDate)
    {
        return (int)((DateTime.Now - birthDate.ToDateTime(new TimeOnly())).TotalDays / FieldValidationConstants.AgeCalculation.AverageDaysPerYear);
    }
}
