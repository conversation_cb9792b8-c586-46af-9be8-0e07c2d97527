namespace CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.ValidationErrors;

/// <summary>
/// Error when employee age is below minimum required age
/// </summary>
public class FieldMinAgeException : Exception, IFieldValidationError
{
    private readonly int _minAge;

    public FieldMinAgeException(int minAge)
    {
        _minAge = minAge;
    }

    public required string PropertyPath { get; init; }
    public required string PropertyLabel { get; init; }
    public string Code => "FIELD_MIN_AGE";
    public override string Message => $"You can not employ someone below {_minAge} years old";
}

/// <summary>
/// Error when employee age is above maximum allowed age
/// </summary>
public class FieldMaxAgeException : Exception, IFieldValidationError
{
    private readonly int _maxAge;

    public FieldMaxAgeException(int maxAge)
    {
        _maxAge = maxAge;
    }

    public required string PropertyPath { get; init; }
    public required string PropertyLabel { get; init; }
    public string Code => "FIELD_MAX_AGE";
    public override string Message => $"You can not employ someone above {_maxAge} years old";
}

/// <summary>
/// Error when child age is below minimum required days
/// </summary>
public class FieldMinChildDaysException : Exception, IFieldValidationError
{
    private readonly int _minDays;

    public FieldMinChildDaysException(int minDays)
    {
        _minDays = minDays;
    }

    public required string PropertyPath { get; init; }
    public required string PropertyLabel { get; init; }
    public string Code => "FIELD_MIN_CHILD_DAYS";
    public override string Message => $"Child minimum age should be {_minDays} day{(_minDays > 1 ? "s" : "")}";
}

/// <summary>
/// Error when spouse age is below minimum required age
/// </summary>
public class FieldMinSpouseAgeException : Exception, IFieldValidationError
{
    private readonly int _minAge;

    public FieldMinSpouseAgeException(int minAge)
    {
        _minAge = minAge;
    }

    public required string PropertyPath { get; init; }
    public required string PropertyLabel { get; init; }
    public string Code => "FIELD_MIN_SPOUSE_AGE";
    public override string Message => $"Spouse minimum age should be {_minAge} years old";
}

/// <summary>
/// Base class for field validation error wrappers
/// </summary>
public abstract class FieldValidationErrorBase
{
    protected FieldValidationErrorBase(IFieldValidationError error)
    {
        Error = error;
    }

    public IFieldValidationError Error { get; }
}

/// <summary>
/// Wrapper for minimum age validation errors
/// </summary>
public class FieldMinAgeError : FieldValidationErrorBase
{
    public FieldMinAgeError(IFieldValidationError error) : base(error)
    {
    }

    public static FieldMinAgeError CreateErrorFrom(FieldMinAgeException ex)
    {
        return new FieldMinAgeError(ex);
    }
}

/// <summary>
/// Wrapper for maximum age validation errors
/// </summary>
public class FieldMaxAgeError : FieldValidationErrorBase
{
    public FieldMaxAgeError(IFieldValidationError error) : base(error)
    {
    }

    public static FieldMaxAgeError CreateErrorFrom(FieldMaxAgeException ex)
    {
        return new FieldMaxAgeError(ex);
    }
}

/// <summary>
/// Wrapper for minimum child days validation errors
/// </summary>
public class FieldMinChildDaysError : FieldValidationErrorBase
{
    public FieldMinChildDaysError(IFieldValidationError error) : base(error)
    {
    }

    public static FieldMinChildDaysError CreateErrorFrom(FieldMinChildDaysException ex)
    {
        return new FieldMinChildDaysError(ex);
    }
}

/// <summary>
/// Wrapper for minimum spouse age validation errors
/// </summary>
public class FieldMinSpouseAgeError : FieldValidationErrorBase
{
    public FieldMinSpouseAgeError(IFieldValidationError error) : base(error)
    {
    }

    public static FieldMinSpouseAgeError CreateErrorFrom(FieldMinSpouseAgeException ex)
    {
        return new FieldMinSpouseAgeError(ex);
    }
}
