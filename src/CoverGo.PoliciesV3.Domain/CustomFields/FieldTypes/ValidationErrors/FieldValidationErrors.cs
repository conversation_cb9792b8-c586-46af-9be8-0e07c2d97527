namespace CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.ValidationErrors;

/// <summary>
/// Represents a string option for string field types
/// </summary>
public record StringOption
{
    public required string Value { get; init; }
    public string? Label { get; init; }
}

/// <summary>
/// Error when extra fields are not allowed
/// </summary>
public class FieldNoExtraAllowedException : IFieldValidationError
{
    public required string PropertyPath { get; init; }
}

/// <summary>
/// Error when field has invalid type
/// </summary>
public class FieldInvalidTypeException : IFieldValidationError
{
    public required string PropertyPath { get; init; }
    public required string PropertyLabel { get; init; }
}

/// <summary>
/// Error when string field has invalid option
/// </summary>
public class FieldInvalidStringOptionException : IFieldValidationError
{
    public required string PropertyPath { get; init; }
    public required string PropertyLabel { get; init; }
    public required List<StringOption> Options { get; init; }
}

/// <summary>
/// Error when field validation fails due to regex pattern
/// </summary>
public class FieldInvalidRegexException : IFieldValidationError
{
    public required string PropertyPath { get; init; }
    public required string PropertyLabel { get; init; }
    public required string Pattern { get; init; }
}

/// <summary>
/// Error when field has invalid number format
/// </summary>
public class FieldInvalidNumberException : IFieldValidationError
{
    public required string PropertyPath { get; init; }
    public required string PropertyLabel { get; init; }
}

/// <summary>
/// Error when string field fails validation
/// </summary>
public class FieldInvalidStringException : IFieldValidationError
{
    public required string PropertyPath { get; init; }
    public required string PropertyLabel { get; init; }
    public required string Validation { get; init; }
}

/// <summary>
/// Error when field has invalid date format
/// </summary>
public class FieldInvalidDateException : IFieldValidationError
{
    public required string PropertyPath { get; init; }
    public required string PropertyLabel { get; init; }
}

/// <summary>
/// Error when required field is missing or empty
/// </summary>
public class FieldRequiredException : IFieldValidationError
{
    public required string PropertyPath { get; init; }
    public required string PropertyLabel { get; init; }
}

/// <summary>
/// Error when one-of validation fails (at least one field from a group must be present)
/// </summary>
public class FieldOneOfException : IFieldValidationError
{
    public required string PropertyPath { get; init; }
    public required List<IFieldValidationError> Errors { get; init; }
}

/// <summary>
/// Wrapper for required field validation errors
/// </summary>
public class FieldRequiredError
{
    public required IFieldValidationError Error { get; init; }

    public static FieldRequiredError CreateErrorFrom(FieldRequiredException ex) =>
        new() { Error = ex };
}
