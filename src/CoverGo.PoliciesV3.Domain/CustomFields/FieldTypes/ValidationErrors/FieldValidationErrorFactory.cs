namespace CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.ValidationErrors;

/// <summary>
/// Factory for creating common field validation errors
/// </summary>
public static class FieldValidationErrorFactory
{
    /// <summary>
    /// Creates a field invalid type error
    /// </summary>
    public static FieldInvalidTypeException CreateInvalidTypeError(PolicyMemberFieldDefinition field) =>
        new() { PropertyPath = field.Name, PropertyLabel = field.Label };

    /// <summary>
    /// Creates a field invalid number error
    /// </summary>
    public static FieldInvalidNumberException CreateInvalidNumberError(PolicyMemberFieldDefinition field) =>
        new() { PropertyPath = field.Name, PropertyLabel = field.Label };

    /// <summary>
    /// Creates a field invalid date error
    /// </summary>
    public static FieldInvalidDateException CreateInvalidDateError(PolicyMemberFieldDefinition field) =>
        new() { PropertyPath = field.Name, PropertyLabel = field.Label };

    /// <summary>
    /// Creates a field required error
    /// </summary>
    public static FieldRequiredException CreateRequiredError(PolicyMemberFieldDefinition field) =>
        new() { PropertyPath = field.Name, PropertyLabel = field.GetFullLabel() };

    /// <summary>
    /// Creates a field no extra allowed error
    /// </summary>
    public static FieldNoExtraAllowedException CreateNoExtraAllowedError(string fieldPath) =>
        new() { PropertyPath = fieldPath };

    /// <summary>
    /// Creates a field invalid string option error
    /// </summary>
    public static FieldInvalidStringOptionException CreateInvalidStringOptionError(PolicyMemberFieldDefinition field, List<StringOption> options) =>
        new() { PropertyPath = field.Name, PropertyLabel = field.Label, Options = options };

    /// <summary>
    /// Creates a single-item error list
    /// </summary>
    public static List<IFieldValidationError> CreateSingleError(IFieldValidationError error) => [error];

    /// <summary>
    /// Creates a field one-of validation error
    /// </summary>
    public static FieldOneOfException CreateOneOfError(string propertyPath, List<IFieldValidationError> errors) =>
        new() { PropertyPath = propertyPath, Errors = errors };

    /// <summary>
    /// Creates an empty error list when no errors exist
    /// </summary>
    public static List<IFieldValidationError>? CreateNoErrors() => null;
}
