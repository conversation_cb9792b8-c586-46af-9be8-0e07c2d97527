namespace CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.Constants;

/// <summary>
/// Constants for field validation rules and patterns
/// </summary>
public static class FieldValidationConstants
{
    /// <summary>
    /// Validation rule names
    /// </summary>
    public static class ValidationRules
    {
        public const string Required = "required";
        public const string Unique = "unique";
        public const string Number = "number";
        public const string MinEmployeeAge = "minEmployeeAge";
        public const string MaxEmployeeAge = "maxEmployeeAge";
        public const string MinChildDays = "minChildDays";
        public const string MinSpouseAge = "minSpouseAge";
    }

    /// <summary>
    /// Member type constants for conditional validation
    /// </summary>
    public static class MemberTypes
    {
        public const string Employee = "employee";
        public const string Child = "child";
        public const string Spouse = "spouse";
    }

    /// <summary>
    /// Field type constants
    /// </summary>
    public static class FieldTypes
    {
        public const string Date = "date";
        public const string Dynamic = "dynamic";
    }

    /// <summary>
    /// Common validation patterns
    /// </summary>
    public static class ValidationPatterns
    {
        public const string NumberRegex = @"^-?\d+(\.\d+)?$";
    }

    /// <summary>
    /// Age calculation constants
    /// </summary>
    public static class AgeCalculation
    {
        public const double AverageDaysPerYear = 365.242199;
    }

    /// <summary>
    /// Validation delimiters
    /// </summary>
    public static class Delimiters
    {
        public const char ValidationSeparator = '|';
        public const char ArgumentSeparator = ':';
        public const char ArgumentValueSeparator = ',';
    }
}
