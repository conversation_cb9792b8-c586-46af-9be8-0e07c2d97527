using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.Extensions;

/// <summary>
/// Extension methods for object conversion and manipulation
/// </summary>
public static class ObjectExtensions
{
    /// <summary>
    /// Converts an object to the specified type or returns default value
    /// </summary>
    public static T? ToObjectOrDefault<T>(this object? obj)
    {
        if (obj == null)
            return default;

        try
        {
            return JToken.FromObject(obj).ToObject<T>();
        }
        catch (JsonException)
        {
            // Handle JSON serialization/deserialization errors
            return default;
        }
        catch (ArgumentException)
        {
            // Handle type conversion errors
            return default;
        }
        catch (InvalidOperationException)
        {
            // Handle invalid operation errors during conversion
            return default;
        }
    }

    /// <summary>
    /// Converts an object to the specified type or returns the specified default value
    /// </summary>
    public static T ToObjectOrDefault<T>(this object? obj, T defaultValue)
    {
        if (obj == null)
            return defaultValue;

        try
        {
            return JToken.FromObject(obj).ToObject<T>() ?? defaultValue;
        }
        catch (JsonException)
        {
            // Handle JSON serialization/deserialization errors
            return defaultValue;
        }
        catch (ArgumentException)
        {
            // Handle type conversion errors
            return defaultValue;
        }
        catch (InvalidOperationException)
        {
            // Handle invalid operation errors during conversion
            return defaultValue;
        }
    }
}
