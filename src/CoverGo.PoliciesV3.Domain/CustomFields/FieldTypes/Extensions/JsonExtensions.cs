using Newtonsoft.Json.Linq;
using System.Diagnostics.CodeAnalysis;

namespace CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.Extensions;

/// <summary>
/// Extension methods for JSON parsing and manipulation
/// </summary>
public static class JsonExtensions
{

    /// <summary>
    /// Tries to parse a string as a JObject
    /// </summary>
    public static bool TryParseAsJObject(this string json, out JObject? jObject)
    {
        try
        {
            jObject = JObject.Parse(json);
            return true;
        }
        catch (Exception)
        {
            jObject = null;
            return false;
        }
    }

    /// <summary>
    /// Converts JObject to Dictionary
    /// </summary>
    [return: NotNullIfNotNull(nameof(jsonObject))]
    public static Dictionary<string, object?>? ToObjectDictionary(this JObject? jsonObject)
    {
        Dictionary<string, object?>? propertyValuePairs = jsonObject?.ToObject<Dictionary<string, object?>?>();
        propertyValuePairs?.ProcessJObjectProperties();
        propertyValuePairs?.ProcessJArrayProperties();
        return propertyValuePairs;
    }

    /// <summary>
    /// Converts JArray to object array
    /// </summary>
    [return: NotNullIfNotNull(nameof(array))]
    public static object?[]? ToObjectArray(this JArray? array) => array?.ToObject<object?[]>()?.Select(ToNetObject).ToArray();



    private static void ProcessJObjectProperties(this IDictionary<string, object?> propertyValuePairs)
    {
        var objectPropertyNames = (from property in propertyValuePairs
                                   let propertyName = property.Key
                                   let value = property.Value
                                   where value is JObject
                                   select propertyName).ToList();

        objectPropertyNames.ForEach(propertyName => propertyValuePairs[propertyName] = ToObjectDictionary(propertyValuePairs[propertyName] as JObject));
    }

    private static void ProcessJArrayProperties(this IDictionary<string, object?> propertyValuePairs)
    {
        var arrayPropertyNames = (from property in propertyValuePairs
                                  let propertyName = property.Key
                                  let value = property.Value
                                  where value is JArray
                                  select propertyName).ToList();

        arrayPropertyNames.ForEach(propertyName => propertyValuePairs[propertyName] = ToObjectArray(propertyValuePairs[propertyName] as JArray));
    }

    [return: NotNullIfNotNull(nameof(value))]
    private static object? ToNetObject(object? value) => value is JObject @object ? @object.ToObjectDictionary() : value is JArray array ? array.ToObjectArray() : value;
}
