using System.Text.RegularExpressions;
using CoverGo.PoliciesV3.Domain.Common.Constants;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.Constants;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.Interfaces;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.ValidationErrors;

namespace CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;

/// <summary>
/// Represents a string field type with optional validation and options
/// </summary>
public sealed record StringFieldType : CustomFieldTypeBase, IOptionsFieldType<StringOption>
{
    public List<StringOption>? Options { get; init; }

    public override List<IFieldValidationError>? ValidateField(object? fieldValue, PolicyMemberFieldDefinition field, IDictionary<string, object?>? otherFields = null)
    {
        if (fieldValue == null)
            return FieldValidationErrorFactory.CreateNoErrors();

        if (fieldValue is not string strValue)
            return FieldValidationErrorFactory.CreateSingleError(FieldValidationErrorFactory.CreateInvalidTypeError(field));

        // Validate against options if they exist
        if (Options != null && !string.IsNullOrEmpty(strValue) && !Options.Select(x => x.Value).Contains(strValue))
            return FieldValidationErrorFactory.CreateSingleError(FieldValidationErrorFactory.CreateInvalidStringOptionError(field, Options));

        // Validate against validation rules
        string[] validations = Validations?.Split(FieldValidationConstants.Delimiters.ValidationSeparator) ?? [];
        foreach (string validation in validations)
        {
            if (validation == FieldValidationConstants.ValidationRules.Number &&
                !Regex.IsMatch(strValue, FieldValidationConstants.ValidationPatterns.NumberRegex, RegexOptions.None, TimeSpan.FromMilliseconds(ValidationConstants.Timeouts.RegexTimeoutMilliseconds)))
            {
                return FieldValidationErrorFactory.CreateSingleError(FieldValidationErrorFactory.CreateInvalidNumberError(field));
            }
            else if (validation.StartsWith("regex:") || (validation.StartsWith('/') && validation.EndsWith('/')))
            {
                string pattern = validation.StartsWith("regex:") ? validation[6..] : validation.Trim('/');
                var regex = new Regex(pattern, RegexOptions.None, TimeSpan.FromMilliseconds(ValidationConstants.Timeouts.RegexTimeoutMilliseconds));
                if (!regex.IsMatch(strValue))
                {
                    return FieldValidationErrorFactory.CreateSingleError(
                        new FieldInvalidStringException { PropertyPath = field.Name, PropertyLabel = field.Label, Validation = validation });
                }
            }
        }

        return FieldValidationErrorFactory.CreateNoErrors();
    }
}




