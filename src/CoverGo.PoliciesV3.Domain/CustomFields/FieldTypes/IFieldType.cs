using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.Constants;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.Interfaces;

namespace CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;

/// <summary>
/// Base interface for field types
/// </summary>
public interface IFieldType
{
    /// <summary>
    /// Tries to parse and validate the field value
    /// </summary>
    List<IFieldValidationError>? TryParseField(object? fieldValue, PolicyMemberFieldDefinition field, out object? parsedFieldValue, IDictionary<string, object?>? otherFields = null);
}

/// <summary>
/// Base interface for field validation errors
/// </summary>
public interface IFieldValidationError
{
    string PropertyPath { get; }
}

/// <summary>
/// Base class for custom field types with validation support
/// </summary>
public abstract record CustomFieldTypeBase : IParsableFieldType
{
    private IEnumerable<ValidationInfo>? _cachedValidationInfo;

    /// <summary>
    /// Validation rules string (e.g., "required|unique")
    /// </summary>
    public string? Validations { get; set; }

    /// <summary>
    /// Parsed validation information (cached for performance)
    /// </summary>
    public IEnumerable<ValidationInfo> ValidationInfo => _cachedValidationInfo ??= ParseValidationInfo();

    /// <summary>
    /// Validates the field value and returns validation errors if any
    /// </summary>
    public abstract List<IFieldValidationError>? ValidateField(object? fieldValue, PolicyMemberFieldDefinition field, IDictionary<string, object?>? otherFields = null);

    /// <summary>
    /// Template method for parsing fields - handles common validation flow
    /// </summary>
    public virtual List<IFieldValidationError>? TryParseField(object? fieldValue, PolicyMemberFieldDefinition field, out object? parsedFieldValue, IDictionary<string, object?>? otherFields = null)
    {
        parsedFieldValue = fieldValue;

        // Step 1: Common validation
        List<IFieldValidationError>? validationErrors = ValidateField(fieldValue, field, otherFields);
        if (validationErrors != null)
        {
            return validationErrors;
        }

        // Step 2: Type-specific parsing (override in derived classes)
        return TryParseFieldSpecific(fieldValue, field, out parsedFieldValue, otherFields);
    }

    /// <summary>
    /// Override this method in derived classes for type-specific parsing logic
    /// </summary>
    protected virtual List<IFieldValidationError>? TryParseFieldSpecific(object? fieldValue, PolicyMemberFieldDefinition field, out object? parsedFieldValue, IDictionary<string, object?>? otherFields = null)
    {
        parsedFieldValue = fieldValue;
        return null; // Default: no additional parsing needed
    }

    private IEnumerable<ValidationInfo> ParseValidationInfo()
    {
        if (string.IsNullOrEmpty(Validations))
            return [];

        return Validations.Split(FieldValidationConstants.Delimiters.ValidationSeparator)
            .Select(validation =>
            {
                string[] parts = validation.Split(FieldValidationConstants.Delimiters.ArgumentSeparator);
                return new ValidationInfo
                {
                    Name = parts[0].Trim(),
                    Arguments = parts.Length > 1
                        ? parts[1].Split(FieldValidationConstants.Delimiters.ArgumentValueSeparator).Select(arg => arg.Trim())
                        : []
                };
            });
    }
}


