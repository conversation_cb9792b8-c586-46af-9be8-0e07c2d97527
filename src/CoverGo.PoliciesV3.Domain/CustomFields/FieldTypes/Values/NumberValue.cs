namespace CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.Values;

/// <summary>
/// Represents a numeric value that can be integer or decimal
/// </summary>
public readonly struct NumberValue
{
    private readonly object _value;

    private NumberValue(object value) => _value = value;

    public static implicit operator NumberValue(int value) => new(value);
    public static implicit operator NumberValue(uint value) => new(value);
    public static implicit operator NumberValue(long value) => new(value);
    public static implicit operator NumberValue(ulong value) => new(value);
    public static implicit operator NumberValue(float value) => new(value);
    public static implicit operator NumberValue(double value) => new(value);
    public static implicit operator NumberValue(decimal value) => new(value);

    public static NumberValue Create(object value) => value switch
    {
        int i => new NumberValue(i),
        uint ui => new NumberValue(ui),
        long l => l,
        ulong ul => ul,
        float f => new NumberValue(f),
        double d => d,
        decimal d => d,
        string s =>
            long.TryParse(s, out long longValue) ? longValue :
            decimal.TryParse(s, out decimal decimalValue) ? decimalValue :
            double.TryParse(s, out double doubleValue) ? doubleValue :
            ulong.TryParse(s, out ulong ulongValue) ? ulongValue :
            throw new ArgumentException($"NumberFieldType - string '{s}' is not a supported number format"),
        _ => throw new ArgumentException($"NumberFieldType - '{value.GetType()}' is not supported"),
    };

    public static NumberValue? CreateNullable(object value) => value switch
    {
        int i => new NumberValue(i),
        uint ui => new NumberValue(ui),
        long l => l,
        ulong ul => ul,
        float f => new NumberValue(f),
        double d => d,
        decimal d => d,
        string s =>
            long.TryParse(s, out long longValue) ? longValue :
            decimal.TryParse(s, out decimal decimalValue) ? decimalValue :
            double.TryParse(s, out double doubleValue) ? doubleValue :
            ulong.TryParse(s, out ulong ulongValue) ? ulongValue :
            null,
        _ => null,
    };

    public object Value => _value;

    public override string ToString() => _value.ToString() ?? string.Empty;
}
