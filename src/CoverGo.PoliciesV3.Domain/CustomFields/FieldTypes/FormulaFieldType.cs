using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.Interfaces;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.ValidationErrors;

namespace CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;

/// <summary>
/// Represents a formula field type that should not be included in uploads
/// </summary>
public record FormulaFieldType : CustomFieldTypeBase, IFormulaFieldType
{
    public IFormulaFunc? Func { get; init; }

    public override List<IFieldValidationError>? ValidateField(object? fieldValue, PolicyMemberFieldDefinition field, IDictionary<string, object?>? otherFields = null) =>
        // Formula fields should not have values in uploads
        fieldValue != null
            ? FieldValidationErrorFactory.CreateSingleError(FieldValidationErrorFactory.CreateNoExtraAllowedError(field.Name))
            : FieldValidationErrorFactory.CreateNoErrors();
}
