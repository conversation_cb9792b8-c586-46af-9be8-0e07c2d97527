using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.ValidationErrors;

namespace CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.Validators;

public sealed class CustomFieldRequiredValidation : ICustomFieldValidation
{
    public required PolicyMemberFieldDefinition Field { get; init; }

    public bool IsRequired { get; init; } = true;

    public IFieldValidationError? ValidateField(object? fieldValue)
    {
        if (!IsRequired)
        {
            return null;
        }

        FieldRequiredException GetException() => new() { PropertyPath = Field.Name, PropertyLabel = Field.GetFullLabel() };

        return fieldValue switch
        {
            null => GetException(),
            string strValue when string.IsNullOrEmpty(strValue) => GetException(),
            _ => null,
        };
    }
}
