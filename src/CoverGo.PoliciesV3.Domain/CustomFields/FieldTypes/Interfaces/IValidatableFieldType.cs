namespace CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.Interfaces;

/// <summary>
/// Interface for field types that support validation
/// </summary>
public interface IValidatableFieldType : IFieldType
{
    /// <summary>
    /// Validates the field value and returns validation errors if any
    /// </summary>
    List<IFieldValidationError>? ValidateField(object? fieldValue, PolicyMemberFieldDefinition field, IDictionary<string, object?>? otherFields = null);
}

/// <summary>
/// Interface for field types that support parsing and validation
/// </summary>
public interface IParsableFieldType : IValidatableFieldType
{
    /// <summary>
    /// Tries to parse and validate the field value
    /// </summary>
    new List<IFieldValidationError>? TryParseField(object? fieldValue, PolicyMemberFieldDefinition field, out object? parsedFieldValue, IDictionary<string, object?>? otherFields = null);
}

/// <summary>
/// Interface for field types that contain nested fields
/// </summary>
public interface INestedFieldType : IFieldType
{
    /// <summary>
    /// Gets the nested field definitions
    /// </summary>
    IReadOnlyList<PolicyMemberFieldDefinition> InnerFieldDefinitions { get; }
}

/// <summary>
/// Interface for field types that support options/choices
/// </summary>
public interface IOptionsFieldType<T> : IFieldType
{
    /// <summary>
    /// Gets the available options for this field
    /// </summary>
    List<T>? Options { get; }
}

/// <summary>
/// Interface for field types that support formula calculations
/// </summary>
public interface IFormulaFieldType : IFieldType
{
    /// <summary>
    /// Gets the formula function for calculations
    /// </summary>
    IFormulaFunc? Func { get; }
}
