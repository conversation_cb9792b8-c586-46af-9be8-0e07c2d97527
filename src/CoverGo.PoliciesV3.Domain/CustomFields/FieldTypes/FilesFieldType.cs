using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.Extensions;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.ValidationErrors;

namespace CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;

/// <summary>
/// Represents a files field type
/// </summary>
public sealed record FilesFieldType : CustomFieldTypeBase
{
    public override List<IFieldValidationError>? ValidateField(object? fieldValue, PolicyMemberFieldDefinition field, IDictionary<string, object?>? otherFields = null)
    {
        if (fieldValue is null)
            return FieldValidationErrorFactory.CreateNoErrors();

        List<Dictionary<string, object>>? files = fieldValue.ToObjectOrDefault<List<Dictionary<string, object>>>();
        if (files == null)
            return FieldValidationErrorFactory.CreateSingleError(FieldValidationErrorFactory.CreateInvalidTypeError(field));

        var validationErrors = new List<IFieldValidationError>();
        foreach (Dictionary<string, object>? dictionary in files)
        {
            if (dictionary is null)
            {
                validationErrors.Add(FieldValidationErrorFactory.CreateInvalidTypeError(field));
            }
        }

        return validationErrors.Count != 0 ? validationErrors : FieldValidationErrorFactory.CreateNoErrors();
    }
}
