using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.Extensions;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.Interfaces;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.ValidationErrors;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.Validators;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;

/// <summary>
/// Represents an object field type with nested field definitions
/// </summary>
public record ObjectFieldType(
    IReadOnlyList<PolicyMemberFieldDefinition> InnerFieldDefinitions,
    bool CheckExtraFields = false) : CustomFieldTypeBase, INestedFieldType
{
    public override List<IFieldValidationError>? TryParseField(object? fieldValue, PolicyMemberFieldDefinition field, out object? parsedFieldValue, IDictionary<string, object?>? otherFields = null)
    {
        parsedFieldValue = fieldValue;
        if (fieldValue is Dictionary<string, object> dictionaryValue)
        {
            fieldValue = JsonConvert.SerializeObject(dictionaryValue);
        }
        else if (fieldValue is string stringValue && stringValue.TryParseAsJObject(out JObject? jObject))
        {
            parsedFieldValue = jObject.ToObjectDictionary();
        }

        List<IFieldValidationError>? errors = ValidateField(fieldValue, field, otherFields);

        return errors;
    }

    public override List<IFieldValidationError>? ValidateField(object? objectFieldValue, PolicyMemberFieldDefinition fieldDefinition, IDictionary<string, object?>? otherFields = null)
    {
        if (objectFieldValue == null)
            return FieldValidationErrorFactory.CreateNoErrors();

        IDictionary<string, object?>? innerFields = objectFieldValue switch
        {
            string json when json.TryParseAsJObject(out JObject? jObject) => jObject.ToObjectDictionary(),
            IDictionary<string, object?> value => value,
            _ => null
        };

        if (innerFields == null)
            return FieldValidationErrorFactory.CreateSingleError(FieldValidationErrorFactory.CreateInvalidTypeError(fieldDefinition));

        List<IFieldValidationError>? errors = null;

        // Filter fields based on condition evaluation
        IReadOnlyList<PolicyMemberFieldDefinition> filteredDefinitions = [.. InnerFieldDefinitions.Where(f =>
            f.Condition is null || f.Condition.Evaluate(innerFields) == true)];

        foreach (PolicyMemberFieldDefinition field in filteredDefinitions)
        {
            object? fieldValue = null;
            innerFields?.TryGetValue(field.Name, out fieldValue);

            var requiredValidation = new CustomFieldRequiredValidation()
            {
                IsRequired = field.IsRequired,
                Field = field
            };

            List<IFieldValidationError>? fieldErrors = field.TryParseField(
                fieldValue,
                out object? parsedFieldValue,
                field.Type is FormulaFieldType
                    ? null
                    : new()
                    {
                        requiredValidation
                    },
                otherFields: otherFields
            );
            if (fieldErrors != null)
            {
                errors ??= [];
                errors.AddRange(fieldErrors);
                continue;
            }

            if (innerFields != null)
            {
                innerFields[field.Name] = parsedFieldValue;
            }
        }

        if (CheckExtraFields)
        {
            var extraFieldErrors = ValidateThatThereAreNoExtraFields(filteredDefinitions, innerFields).ToList();
            if (extraFieldErrors.Count > 0)
            {
                errors ??= [];
                errors.AddRange(extraFieldErrors);
            }
        }

        return errors ?? FieldValidationErrorFactory.CreateNoErrors();
    }

    private static IEnumerable<IFieldValidationError> ValidateThatThereAreNoExtraFields(IReadOnlyList<PolicyMemberFieldDefinition>? fields, IDictionary<string, object?>? fieldValues)
    {
        if (fields == null)
            yield break;

        IEnumerable<string>? extraFields = fieldValues?.Keys.Except(fields.Select(it => it.Name));
        if (extraFields != null && extraFields.Any())
        {
            foreach (string field in extraFields)
            {
                yield return FieldValidationErrorFactory.CreateNoExtraAllowedError(field);
            }
        }
    }
}


