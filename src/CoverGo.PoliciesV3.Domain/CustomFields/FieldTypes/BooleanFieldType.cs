using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.ValidationErrors;

namespace CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;

/// <summary>
/// Represents a boolean field type
/// </summary>
public sealed record BooleanFieldType : CustomFieldTypeBase
{
    public override List<IFieldValidationError>? ValidateField(object? fieldValue, PolicyMemberFieldDefinition field, IDictionary<string, object?>? otherFields = null) =>
        fieldValue switch
        {
            null or bool => FieldValidationErrorFactory.CreateNoErrors(),
            string fieldValueStr when bool.TryParse(fieldValueStr, out _) => FieldValidationErrorFactory.CreateNoErrors(),
            _ => FieldValidationErrorFactory.CreateSingleError(FieldValidationErrorFactory.CreateInvalidTypeError(field))
        };

    protected override List<IFieldValidationError>? TryParseFieldSpecific(object? fieldValue, PolicyMemberFieldDefinition field, out object? parsedFieldValue, IDictionary<string, object?>? otherFields = null)
    {
        parsedFieldValue = fieldValue;

        if (fieldValue is string fieldValueStr && bool.TryParse(fieldValueStr, out bool parsedBoolValue))
        {
            parsedFieldValue = parsedBoolValue;
        }

        return null;
    }
}
