namespace CoverGo.PoliciesV3.Domain.CustomFields;

/// <summary>
/// Static class for identifying identity fields
/// </summary>
public static class IdentificationIdField
{
    private static readonly HashSet<string> IdentityFieldNames = new(StringComparer.OrdinalIgnoreCase)
    {
        "id_number",
        "identification_id",
        "identity_number",
        "passport_number",
        "national_id",
        "social_security_number",
        "ssn"
    };

    /// <summary>
    /// Determines if a field name represents an identification field
    /// </summary>
    public static bool IsIdentificationId(string fieldName)
    {
        return !string.IsNullOrWhiteSpace(fieldName) && IdentityFieldNames.Contains(fieldName);
    }
}
