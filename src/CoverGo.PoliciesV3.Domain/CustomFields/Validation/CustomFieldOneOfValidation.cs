using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.ValidationErrors;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.Validators;

namespace CoverGo.PoliciesV3.Domain.CustomFields.Validation;

/// <summary>
/// Represents a one-of validation rule where at least one field from a group must be present
/// </summary>
public sealed class CustomFieldOneOfValidation
{
    private IReadOnlyList<CustomFieldRequiredValidation> _validations = default!;

    public required IReadOnlyList<CustomFieldRequiredValidation> Validations
    {
        get => _validations;
        init
        {
            if (value.Count == 0)
            {
                throw new InvalidOperationException("OneOf validations can't have 0 validations inside");
            }
            _validations = value;
        }
    }

    /// <summary>
    /// Validates fields and returns a FieldOneOfException if validation fails, null if successful
    /// </summary>
    public FieldOneOfException? EnsureValidFields(IDictionary<string, object?>? fields)
    {
        FieldOneOfException? validationError = ValidateFields(fields);
        return validationError is null ? null : FieldValidationErrorFactory.CreateOneOfError(
            GetGroupPropertyPath(),
            [.. validationError.Errors.Select(error => error switch
            {
                FieldRequiredException ex => FieldRequiredError.CreateErrorFrom(ex).Error,
                _ => throw new NotSupportedException($"Validation error type '{error.GetType().Name}' is not supported for one-of validation.")
            })]);
    }

    /// <summary>
    /// Validates fields and returns a FieldOneOfException with all validation errors if none pass
    /// </summary>
    public FieldOneOfException? ValidateFields(IDictionary<string, object?>? fields)
    {
        var errors = new List<IFieldValidationError>();

        foreach (CustomFieldRequiredValidation validation in Validations)
        {
            object? fieldValue = fields?.TryGetValue(validation.Field.Name, out object? value) == true ? value : null;
            IFieldValidationError? validationError = validation.ValidateField(fieldValue);

            if (validationError is null)
            {
                // At least one field is valid, so one-of validation passes
                return null;
            }

            errors.Add(validationError);
        }

        // All fields failed validation, return the one-of exception
        return FieldValidationErrorFactory.CreateOneOfError(GetGroupPropertyPath(), errors);
    }

    /// <summary>
    /// Gets a representative property path for the group of fields
    /// </summary>
    private string GetGroupPropertyPath() =>
        Validations.Count > 0
            ? $"OneOf({string.Join(",", Validations.Select(v => v.Field.Name))})"
            : "OneOf(empty)";
}
