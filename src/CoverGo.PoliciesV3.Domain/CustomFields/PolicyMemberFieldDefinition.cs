using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.Validators;

namespace CoverGo.PoliciesV3.Domain.CustomFields;

/// <summary>
/// Represents a field definition for policy member custom fields
/// </summary>
public sealed record PolicyMemberFieldDefinition : CustomFieldDefinition
{
    /// <summary>
    /// Whether the field is required for dependents.
    /// </summary>
    public bool IsRequiredForDependent { get; init; }

    /// <summary>
    /// Whether this field is an identity field
    /// </summary>
    public bool IsIdentityField => IdentificationIdField.IsIdentificationId(Name);

    /// <summary>
    /// Conditional logic for this field
    /// </summary>
    public CustomFieldCondition? Condition { get; init; }

    /// <summary>
    /// Tries to parse and validate the field value with optional validations
    /// </summary>
    public List<IFieldValidationError>? TryParseField(object? fieldValue, out object? parsedFieldValue, List<ICustomFieldValidation>? validations = null, IDictionary<string, object?>? otherFields = null)
    {
        parsedFieldValue = null;
        if (validations is not null)
        {
            foreach (ICustomFieldValidation validation in validations)
            {
                IFieldValidationError? validationError = validation.ValidateField(fieldValue);
                if (validationError is not null)
                {
                    return [validationError];
                }
            }
        }
        List<IFieldValidationError>? errors = Type.TryParseField(fieldValue, this, out parsedFieldValue, otherFields);

        return errors;
    }
}
