using System.Linq.Expressions;
using System.Text.RegularExpressions;
using CoverGo.PoliciesV3.Domain.Common.Constants;

namespace CoverGo.PoliciesV3.Domain.CustomFields;

public record CustomFieldCondition(string Condition)
{
    public static implicit operator CustomFieldCondition?(string? condition)
    {
        return string.IsNullOrWhiteSpace(condition) ? null : new CustomFieldCondition(condition);
    }

    private Func<IDictionary<string, object?>?, bool>? _conditionFunc;

    private Func<IDictionary<string, object?>?, bool>? ConditionFunc
    {
        get
        {
            if (_conditionFunc is null && !string.IsNullOrWhiteSpace(Condition))
            {
                _conditionFunc = Predicate(Condition);
            }

            return _conditionFunc;
        }
    }

    private static Func<IDictionary<string, object?>?, bool>? Predicate(string input)
    {
        const string formKitIfExpPattern = @"((?!\|\||&&).+)(\|\||&&)((?!\|\||&&).+)";
        Match match = Match(input, formKitIfExpPattern);
        return match.Success && match.Groups.Count == 4
            ? Combine(Predicate(match.Groups[1].Value), LogicalOperator(match.Groups[2].Value), Compare(match.Groups[3].Value))
            : Compare(input);
    }

    private static Func<IDictionary<string, object?>?, bool>? Combine(
        Func<IDictionary<string, object?>?, bool>? left,
        ExpressionType expressionType,
        Func<IDictionary<string, object?>?, bool>? right) =>
        left is not null && right is not null
            ? expressionType switch {
                ExpressionType.OrElse => contextFields => left(contextFields) || right(contextFields),
                ExpressionType.AndAlso => contextFields => left(contextFields) && right(contextFields),
                _ => null
            }
            : left ?? right;

    private static ExpressionType LogicalOperator(string input) => input switch {
        "||" => ExpressionType.OrElse,
        "&&" => ExpressionType.AndAlso,
        _ => ExpressionType.OrElse
    };

    private static Func<IDictionary<string, object?>?, bool>? Compare(string input)
    {
        const string comparisonPattern = @"\s*\$get\((\w+)\)\.value\s*={2,3}\s*([^'""\s]+|'[^']+'|""[^""]+"")\s*";
        Match match = Match(input, comparisonPattern);
        if (match.Success && match.Groups.Count > 2)
        {
            string dependentFieldName = match.Groups[1].Value;
            object parsedValue = ParseValue(match.Groups[2].Value);

            return contextFields => Equals(contextFields, dependentFieldName, parsedValue);
        }

        return null;
    }

    private static bool Equals(IDictionary<string, object?>? contextFields, string dependentFieldName, object? parsedValue)
    {
        if (contextFields?.TryGetValue(dependentFieldName, out object? dependentFieldValue) == true)
        {
            if (dependentFieldValue?.Equals(parsedValue) == true)
            {
                return true;
            }
        }

        return false;
    }

    private static Match Match(string input, string pattern) =>
        Regex.Match(input, pattern, RegexOptions.Compiled, TimeSpan.FromMilliseconds(ValidationConstants.Timeouts.RegexTimeoutMilliseconds));

    private static object ParseValue(string value)
    {
        const string singleQuotePattern = @"'(.+)'";
        const string doubleQuotePattern = @"""(.+)""";
        Match textEnclosedBySingleQuote = Match(value, singleQuotePattern);
        Match textEnclosedByDoubleQuote = Match(value, doubleQuotePattern);
        if (textEnclosedBySingleQuote.Success && textEnclosedBySingleQuote.Groups.Count > 1)
        {
            return textEnclosedBySingleQuote.Groups[1].Value;
        }
        else if (textEnclosedByDoubleQuote.Success && textEnclosedByDoubleQuote.Groups.Count > 1)
        {
            return textEnclosedByDoubleQuote.Groups[1].Value;
        }
        else if (bool.TryParse(value, out bool boolean))
        {
            return boolean;
        }
        else if (int.TryParse(value, out int number))
        {
            return number;
        }
        else if (double.TryParse(value, out double amount))
        {
            return amount;
        }
        return value;
    }

    public bool? Evaluate(IDictionary<string, object?>? contextFields) =>
        ConditionFunc?.Invoke(contextFields);
}
