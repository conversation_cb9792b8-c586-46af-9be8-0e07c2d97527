using CoverGo.PoliciesV3.Domain.Common;

namespace CoverGo.PoliciesV3.Domain.PolicyMembers.Enums;

/// <summary>
/// Represents the decision type for member underwriting
/// </summary>
public record MemberUnderwritingDecisionType(string Value) : ValueObject<string>(Value)
{
    public static MemberUnderwritingDecisionType Accept => new("ACCEPT");
    public static MemberUnderwritingDecisionType Reject => new("REJECT");
    public static MemberUnderwritingDecisionType Refer => new("REFER");
    public static MemberUnderwritingDecisionType Postpone => new("POSTPONE");
}
