using CoverGo.PoliciesV3.Domain.Common;

namespace CoverGo.PoliciesV3.Domain.PolicyMembers.Enums;

/// <summary>
/// Represents the underwriting status for member benefits
/// </summary>
public record MemberBenefitUnderwritingStatus(string Value) : ValueObject<string>(Value)
{
    public static MemberBenefitUnderwritingStatus Pending => new("PENDING");
    public static MemberBenefitUnderwritingStatus Approved => new("APPROVED");
    public static MemberBenefitUnderwritingStatus Rejected => new("REJECTED");
    public static MemberBenefitUnderwritingStatus Excluded => new("EXCLUDED");
    public static MemberBenefitUnderwritingStatus Modified => new("MODIFIED");
}
