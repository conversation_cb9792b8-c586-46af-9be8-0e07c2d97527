using CoverGo.PoliciesV3.Domain.Common;

namespace CoverGo.PoliciesV3.Domain.PolicyMembers.Enums;

/// <summary>
/// Represents the source type for underwriting decisions
/// </summary>
public record UnderwritingDecisionSourceType(string Value) : ValueObject<string>(Value)
{
    public static UnderwritingDecisionSourceType Auto => new("AUTO");
    public static UnderwritingDecisionSourceType Manual => new("MANUAL");
    public static UnderwritingDecisionSourceType System => new("SYSTEM");
}
