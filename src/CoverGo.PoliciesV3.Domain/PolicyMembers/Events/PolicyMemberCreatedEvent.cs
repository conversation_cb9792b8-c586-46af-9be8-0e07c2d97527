using CoverGo.PoliciesV3.Domain.Common;
using CoverGo.PoliciesV3.Domain.Policies;

namespace CoverGo.PoliciesV3.Domain.PolicyMembers.Events;

public class PolicyMemberCreatedEvent(PolicyId aggregateId) : DomainEvent<PolicyId>(aggregateId)
{
    public required PolicyMemberId PolicyMemberId { get; init; }
    public required string MemberId { get; init; }
    public DateOnly? StartDate { get; init; }
    public DateOnly? EndDate { get; init; }
    public required string PlanId { get; init; }
    public PolicyMemberId? DependentOfId { get; init; } = null;
    public Guid? IndividualId { get; init; } = null;
    public ICollection<PolicyField> Fields { get; init; } = new HashSet<PolicyField>();
}