using CoverGo.BuildingBlocks.Domain.Core.Entities;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMembers.Events;
using CoverGo.PoliciesV3.Domain.ValueObjects;
using System.ComponentModel.DataAnnotations;

namespace CoverGo.PoliciesV3.Domain.PolicyMembers;

public class PolicyMember : AggregateRootBase<PolicyMemberId>
{
    #region Constructors

    private PolicyMember(PolicyMemberId id) : base(id)
    {
    }

    public PolicyMember() : this(PolicyMemberId.Empty)
    {
    }

    #endregion

    #region Fields / Properties

    // Entity Identifiers
    public required string MemberId { get; init; }
    public required PolicyId PolicyId { get; init; }
    public PolicyMemberId? DependentOfId { get; private set; }
    public Guid? IndividualId { get; private set; }

    // State Fields
    public bool IsRemoved { get; private set; }
    public bool IsPrinted { get; private set; }
    public bool IsRenewed { get; private set; }
    public string? CertificateNumber { get; private set; }

    // Collections
    public virtual ICollection<PolicyMemberState> States { get; set; } = [];

    // Concurrency Control - maps to PostgreSQL xmin system column
    [Timestamp]
    public uint RowVersion { get; set; }

    // TODO: Determine if these quote-related fields are needed:
    // - QuoteMemberId: Links to original quote member
    // - QuoteId: Links to original quote
    // - ExternalValidationResult: Stores external validation state

    #endregion

    #region Navigation Properties

    public Policy? Policy { get; set; }
    public virtual PolicyMember? DependentOf { get; private set; }

    #endregion

    #region Computed Properties

    /// <summary>
    /// Checks if this member is currently active
    /// </summary>
    public bool IsActive => States.Any(s => s.IsActiveOn(DateOnly.FromDateTime(DateTime.UtcNow)));

    /// <summary>
    /// Checks if this member is a dependent of another member
    /// </summary>
    public bool IsDependent => DependentOfId != null;

    /// <summary>
    /// Gets the current active state
    /// </summary>
    public PolicyMemberState? CurrentState => GetActiveStateOn(DateOnly.FromDateTime(DateTime.UtcNow));

    #endregion

    #region Factory Methods

    public static PolicyMember Create(
        PolicyId policyId,
        string memberId,
        DateOnly? startDate,
        DateOnly? endDate,
        string planId,
        PolicyMemberId? dependentOfId = null,
        Guid? individualId = null,
        ICollection<PolicyField>? fields = null)
    {
        var policyMember = new PolicyMember(PolicyMemberId.New)
        {
            PolicyId = policyId,
            MemberId = memberId,
            IndividualId = individualId,
            DependentOfId = dependentOfId
        };

        var @event = new PolicyMemberCreatedEvent(policyId)
        {
            PolicyMemberId = policyMember.Id,
            MemberId = memberId,
            StartDate = startDate,
            EndDate = endDate,
            PlanId = planId,
            DependentOfId = dependentOfId,
            IndividualId = individualId,
            Fields = fields ?? []
        };

        policyMember.AddDomainEvent(@event);

        // Create initial state if we have valid dates and plan
        if (startDate.HasValue && endDate.HasValue && !string.IsNullOrEmpty(planId))
        {
            var initialState = PolicyMemberState.Create(
                policyMember.Id,
                startDate.Value,
                endDate.Value,
                planId,
                null,
                null,
                fields);

            policyMember.States.Add(initialState);
        }

        return policyMember;
    }

    #endregion

    #region State Management

    /// <summary>
    /// Adds a new state to the policy member
    /// </summary>
    public void AddState(PolicyMemberState newState)
    {
        if (!newState.IsValid)
            throw new InvalidOperationException($"Invalid state: {nameof(PolicyMemberState.EndDate)} must be after or equal to {nameof(PolicyMemberState.StartDate)}");

        EnsureValidStateDates(States.Concat(new[] { newState }));

        States.Add(newState);

        AddDomainEvent(new PolicyMemberStateAddedEvent(PolicyId, Id, newState));
    }

    /// <summary>
    /// Removes a state from the policy member
    /// </summary>
    public void RemoveState(PolicyMemberState stateToRemove)
    {
        States.Remove(stateToRemove);

        AddDomainEvent(new PolicyMemberStateRemovedEvent(PolicyId, Id, stateToRemove));
    }

    /// <summary>
    /// Gets the active state on a specific date
    /// </summary>
    public PolicyMemberState? GetActiveStateOn(DateOnly date)
        => States.FirstOrDefault(s => s.IsActiveOn(date));

    /// <summary>
    /// Gets all states that are active during a date range
    /// </summary>
    public IEnumerable<PolicyMemberState> GetActiveStatesDuring(DateOnly startDate, DateOnly endDate)
        => States.Where(s => s.StartDate <= endDate && s.EndDate >= startDate);

    #endregion

    #region Private Methods

    /// <summary>
    /// Validates that states do not overlap and are in chronological order
    /// </summary>
    private static void EnsureValidStateDates(IEnumerable<PolicyMemberState> states)
    {
        var stateList = states.OrderBy(s => s.StartDate).ToList();

        for (int i = 0; i < stateList.Count; i++)
        {
            PolicyMemberState currentState = stateList[i];

            if (!currentState.IsValid)
                throw new InvalidOperationException($"Invalid state: {nameof(PolicyMemberState.EndDate)} {currentState.EndDate} must be after or equal to {nameof(PolicyMemberState.StartDate)} {currentState.StartDate}");

            // Check for overlaps with next state
            if (i < stateList.Count - 1)
            {
                PolicyMemberState nextState = stateList[i + 1];
                if (currentState.OverlapsWith(nextState))
                    throw new InvalidOperationException($"State overlap detected: State ending {currentState.EndDate} overlaps with state starting {nextState.StartDate}");
            }
        }
    }

    #endregion
}