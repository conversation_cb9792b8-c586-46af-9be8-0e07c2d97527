namespace CoverGo.PoliciesV3.Domain.Common.Enums;

/// <summary>
/// Represents the type of premium pricing component
/// </summary>
public record PolicyPremiumPricingComponentType(string Value) : ValueObject<string>(Value)
{
    public static PolicyPremiumPricingComponentType Undefined => new("Undefined");
    public static PolicyPremiumPricingComponentType Gross => new("Gross");
    public static PolicyPremiumPricingComponentType GrossWithTaxes => new("GrossWithTaxes");
    public static PolicyPremiumPricingComponentType Net => new("Net");
    public static PolicyPremiumPricingComponentType Taxes => new("Taxes");
    public static PolicyPremiumPricingComponentType Commissions => new("Commissions");
    public static PolicyPremiumPricingComponentType Fees => new("Fees");
    public static PolicyPremiumPricingComponentType Loadings => new("Loadings");
    public static PolicyPremiumPricingComponentType BasePremium => new("BasePremium");
    public static PolicyPremiumPricingComponentType Campaigns => new("Campaigns");
    public static PolicyPremiumPricingComponentType TotalPremium => new("TotalPremium");
    public static PolicyPremiumPricingComponentType Discounts => new("Discounts");
    public static PolicyPremiumPricingComponentType Limits => new("Limits");
}
