namespace CoverGo.PoliciesV3.Domain.Common.Enums;

/// <summary>
/// Represents the status type for loading
/// </summary>
public record LoadingStatusType(string Value) : ValueObject<string>(Value)
{
    public static LoadingStatusType Pending => new("Pending");
    public static LoadingStatusType Accepted => new("Accepted");
    public static LoadingStatusType Rejected => new("Rejected");
    public static LoadingStatusType Issued => new("Issued");
}
