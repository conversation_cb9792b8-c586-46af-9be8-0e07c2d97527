namespace CoverGo.PoliciesV3.Domain.Common;

/// <summary>
/// Simple implementation of IUserError for representing user-facing errors
/// </summary>
public class UserError(string code, string message) : IUserError
{
    public string Code { get; } = code ?? throw new ArgumentNullException(nameof(code));
    public string Message { get; } = message ?? throw new ArgumentNullException(nameof(message));
}


