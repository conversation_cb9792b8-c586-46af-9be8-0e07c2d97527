namespace CoverGo.PoliciesV3.Domain.Common;

/// <summary>
/// Base class for all domain exceptions that represent business rule violations or domain errors.
/// Implements IUserError to provide structured error information for API responses.
/// </summary>
public abstract class DomainException : Exception, IUserError
{
    /// <summary>
    /// Initializes a new instance of the DomainException class with a specified error message.
    /// </summary>
    /// <param name="message">The message that describes the error.</param>
    protected DomainException(string message) : base(message)
    {
    }

    /// <summary>
    /// Initializes a new instance of the DomainException class with a specified error message and inner exception.
    /// </summary>
    /// <param name="message">The message that describes the error.</param>
    /// <param name="innerException">The exception that is the cause of the current exception.</param>
    protected DomainException(string message, Exception innerException) : base(message, innerException)
    {
    }

    /// <summary>
    /// Gets the error code that categorizes this domain exception.
    /// This code should be stable and suitable for programmatic error handling.
    /// </summary>
    public abstract string Code { get; }
}
