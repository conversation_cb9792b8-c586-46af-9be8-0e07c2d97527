namespace CoverGo.PoliciesV3.Domain.Common.Constants;

/// <summary>
/// Constants for validation operations to avoid magic numbers.
/// Centralizes all validation-related configuration values.
/// </summary>
public static class ValidationConstants
{
    /// <summary>
    /// Timeout values for validation operations
    /// </summary>
    public static class Timeouts
    {
        /// <summary>
        /// Timeout in milliseconds for regex pattern matching operations.
        /// Prevents denial of service attacks from catastrophic backtracking.
        /// </summary>
        public const int RegexTimeoutMilliseconds = 3000;
    }
}
