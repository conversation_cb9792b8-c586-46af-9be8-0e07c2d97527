using CoverGo.PoliciesV3.Domain.PolicyMembers.Enums;

namespace CoverGo.PoliciesV3.Domain.ValueObjects;

/// <summary>
/// Represents an underwriting decision for a member
/// </summary>
public record MemberUnderwritingDecision
{
    public required string Id { get; init; }
    public required MemberUnderwritingDecisionType Type { get; init; }
    public required UnderwritingDecisionSourceType Source { get; init; }
    public string? Reason { get; init; }
    public string? Comments { get; init; }
    public required DateOnly DecisionDate { get; init; }
    public string? DecisionBy { get; init; }
}
