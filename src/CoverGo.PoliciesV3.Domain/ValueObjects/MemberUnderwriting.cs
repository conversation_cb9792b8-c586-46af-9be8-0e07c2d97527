using CoverGo.PoliciesV3.Domain.PolicyMembers.Enums;

namespace CoverGo.PoliciesV3.Domain.ValueObjects;

/// <summary>
/// Represents the main underwriting information for a member
/// </summary>
public record MemberUnderwriting
{
    public required string Id { get; init; }
    public required string Name { get; init; }
    public MemberUnderwritingDecision? Decision { get; init; }
    public ICollection<MemberUnderwritingExclusion> Exclusions { get; init; } = [];
    public ICollection<MemberUnderwritingPreExistingCondition> PreExistingConditions { get; init; } = [];
    public required MemberUnderwritingStatus Status { get; init; }
}
