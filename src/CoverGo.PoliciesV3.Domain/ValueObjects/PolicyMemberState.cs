using CoverGo.BuildingBlocks.Domain.Core.Entities;
using CoverGo.PoliciesV3.Domain.Endorsements;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMembers;
using CoverGo.PoliciesV3.Domain.PolicyMembers.Enums;
using System.ComponentModel.DataAnnotations;

namespace CoverGo.PoliciesV3.Domain.ValueObjects;

/// <summary>
/// Represents a temporal state of a policy member with specific coverage details
/// </summary>
public class PolicyMemberState : Entity<PolicyMemberStateId>
{
    #region Constructors

    private PolicyMemberState(PolicyMemberStateId id) : base(id)
    {
    }

    public PolicyMemberState() : this(PolicyMemberStateId.Empty)
    {
    }

    #endregion

    #region Fields / Properties

    // Required Properties
    public required PolicyMemberId PolicyMemberId { get; init; }
    public required DateOnly StartDate { get; init; }
    public required DateOnly EndDate { get; init; }
    public required string PlanId { get; init; }

    // Optional Properties
    public string? Class { get; init; }
    public EndorsementId? EndorsementId { get; set; }
    public string? HealthQuestionnaireResponseId { get; set; }

    // Validation & Underwriting
    public PolicyMemberValidationResult? ValidationResult { get; set; }
    public PolicyMemberUnderwritingResult? UnderwritingResult { get; set; }
    public MemberUnderwriting? Underwriting { get; set; }
    public MemberPremium? Premium { get; set; }

    // Collections
    public ICollection<MemberLoading>? Loadings { get; set; } = [];
    public ICollection<MemberBenefitsLoading>? BenefitsLoadings { get; set; } = [];
    public ICollection<MemberBenefitsUnderwriting>? BenefitsUnderwritings { get; set; } = [];
    public virtual ICollection<PolicyField> Fields { get; set; } = [];

    // Concurrency Control - maps to PostgreSQL xmin system column
    [Timestamp]
    public uint RowVersion { get; set; }

    #endregion

    #region Navigation Properties

    public PolicyMember? PolicyMember { get; set; }

    #endregion

    #region Computed Properties

    /// <summary>
    /// Checks if this state is valid (end date after start date)
    /// </summary>
    public bool IsValid => EndDate >= StartDate;

    /// <summary>
    /// Gets the duration of this state in days
    /// </summary>
    public int DurationInDays => EndDate.DayNumber - StartDate.DayNumber + 1;

    #endregion

    #region Factory Methods

    /// <summary>
    /// Creates a policy member state
    /// </summary>
    public static PolicyMemberState Create(
        PolicyMemberId policyMemberId,
        DateOnly startDate,
        DateOnly endDate,
        string planId,
        string? @class = null,
        EndorsementId? endorsementId = null,
        ICollection<PolicyField>? customFields = null) => new(PolicyMemberStateId.New)
    {
        PolicyMemberId = policyMemberId,
        StartDate = startDate,
        EndDate = endDate,
        PlanId = planId,
        Class = @class,
        EndorsementId = endorsementId,
        Fields = customFields ?? []
    };

    #endregion

    #region Business Logic

    /// <summary>
    /// Checks if this state overlaps with another state
    /// </summary>
    public bool OverlapsWith(PolicyMemberState other)
        => StartDate <= other.EndDate && EndDate >= other.StartDate;

    /// <summary>
    /// Checks if the state is active on a specific date
    /// </summary>
    public bool IsActiveOn(DateOnly date)
        => date >= StartDate && date <= EndDate;

    #endregion
}