using CoverGo.PoliciesV3.Domain.Common;

namespace CoverGo.PoliciesV3.Domain.Endorsements.Exceptions;

/// <summary>
/// Exception thrown when an endorsement cannot be found
/// </summary>
public class EndorsementNotFoundException : DomainException
{
    public EndorsementNotFoundException(string policyId, string endorsementId)
        : base($"Endorsement {endorsementId} not found for policy {policyId}")
    {
        PolicyId = policyId;
        EndorsementId = endorsementId;
    }

    public EndorsementNotFoundException(string policyId, string endorsementId, Exception innerException)
        : base($"Endorsement {endorsementId} not found for policy {policyId}", innerException)
    {
        PolicyId = policyId;
        EndorsementId = endorsementId;
    }

    public override string Code => "ENDORSEMENT_NOT_FOUND";

    public string PolicyId { get; }
    public string EndorsementId { get; }
}
