using CoverGo.PoliciesV3.Domain.Common;

namespace CoverGo.PoliciesV3.Domain.Endorsements.Exceptions;

/// <summary>
/// Exception thrown when effective date is outside policy date range
/// </summary>
public class EffectiveDateOutsidePolicyDatesException : DomainException
{
    public EffectiveDateOutsidePolicyDatesException(string policyId)
        : base($"Effective date is outside policy {policyId} date range")
    {
        PolicyId = policyId;
    }

    public EffectiveDateOutsidePolicyDatesException(string policyId, Exception innerException)
        : base($"Effective date is outside policy {policyId} date range", innerException)
    {
        PolicyId = policyId;
    }

    public override string Code => "EFFECTIVE_DATE_OUTSIDE_POLICY_DATES";

    public string PolicyId { get; }
}
