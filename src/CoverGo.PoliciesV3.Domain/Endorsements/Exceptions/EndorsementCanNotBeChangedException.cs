using CoverGo.PoliciesV3.Domain.Common;

namespace CoverGo.PoliciesV3.Domain.Endorsements.Exceptions;

/// <summary>
/// Exception thrown when an endorsement cannot be changed due to its status
/// </summary>
public class EndorsementCanNotBeChangedException : DomainException
{
    public EndorsementCanNotBeChangedException(string policyId, string endorsementId, string status)
        : base($"Endorsement {endorsementId} for policy {policyId} cannot be changed due to status {status}")
    {
        PolicyId = policyId;
        EndorsementId = endorsementId;
        Status = status;
    }

    public EndorsementCanNotBeChangedException(string policyId, string endorsementId, string status, Exception innerException)
        : base($"Endorsement {endorsementId} for policy {policyId} cannot be changed due to status {status}", innerException)
    {
        PolicyId = policyId;
        EndorsementId = endorsementId;
        Status = status;
    }

    public override string Code => "ENDORSEMENT_CANNOT_BE_CHANGED";

    public string PolicyId { get; }
    public string EndorsementId { get; }
    public string Status { get; }
}
