using CoverGo.BuildingBlocks.Domain.Core.Entities;
using CoverGo.PoliciesV3.Domain.Policies;
using System.ComponentModel.DataAnnotations;

namespace CoverGo.PoliciesV3.Domain.Endorsements;

/// <summary>
/// Represents a policy endorsement (minimal implementation for upload validation)
/// </summary>
public class Endorsement : Entity<EndorsementId>
{
    #region Constructors

    private Endorsement(EndorsementId id) : base(id)
    {
    }

    public Endorsement() : this(EndorsementId.Empty)
    {
    }

    #endregion

    #region Properties

    /// <summary>
    /// Foreign key to the Policy
    /// </summary>
    public PolicyId PolicyId { get; private set; } = PolicyId.Empty;

    /// <summary>
    /// Current status of the endorsement
    /// </summary>
    public string Status { get; private set; } = EndorsementStatus.Draft;

    /// <summary>
    /// Type of endorsement (e.g., MemberMovement, CancelPolicy)
    /// </summary>
    public string? Type { get; private set; }

    /// <summary>
    /// Effective date of the endorsement
    /// </summary>
    public DateTime? EffectiveDate { get; private set; }

    /// <summary>
    /// Concurrency control - maps to PostgreSQL xmin system column
    /// </summary>
    [Timestamp]
    public uint RowVersion { get; set; }

    #endregion

    #region Navigation Properties

    /// <summary>
    /// Navigation property to Policy
    /// </summary>
    public Policy? Policy { get; set; }

    #endregion

    #region Factory Methods

    /// <summary>
    /// Creates a new endorsement
    /// </summary>
    public static Endorsement Create(
        PolicyId policyId,
        string status = EndorsementStatus.Draft,
        string? type = null,
        DateTime? effectiveDate = null) => new(EndorsementId.New)
        {
            PolicyId = policyId,
            Status = status,
            Type = type,
            EffectiveDate = effectiveDate
        };

    #endregion

    #region Business Logic

    /// <summary>
    /// Determines if the endorsement can be changed based on its status
    /// </summary>
    public bool CanBeChanged() => Status switch
    {
        EndorsementStatus.Approved => false,
        EndorsementStatus.Rejected => false,
        EndorsementStatus.Canceled => false,
        _ => true
    };

    /// <summary>
    /// Updates the endorsement status
    /// </summary>
    public void UpdateStatus(string status) => Status = status;

    #endregion
}


