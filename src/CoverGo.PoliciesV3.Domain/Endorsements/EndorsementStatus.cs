namespace CoverGo.PoliciesV3.Domain.Endorsements;

/// <summary>
/// Constants for endorsement status values
/// </summary>
public static class EndorsementStatus
{
    public const string Draft = "DRAFT";
    public const string Created = "CREATED";
    public const string Approved = "APPROVED";
    public const string Rejected = "REJECTED";
    public const string Canceled = "CANCELED";
    public const string TransactionSubmitted = "TRANSACTION_SUBMITTED";
    public const string Paid = "PAID";
    public const string InProgress = "IN_PROGRESS";
    public const string AwaitingApproval = "AWAITING_APPROVAL";
    public const string Submitted = "SUBMITTED";

    /// <summary>
    /// Statuses that should not be accounted for in business logic
    /// </summary>
    public static readonly string[] DoNotAccountFor = [Rejected, Canceled];
}
