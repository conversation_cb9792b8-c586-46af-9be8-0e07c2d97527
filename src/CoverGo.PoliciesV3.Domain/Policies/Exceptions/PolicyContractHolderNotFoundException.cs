using CoverGo.PoliciesV3.Domain.Common;

namespace CoverGo.PoliciesV3.Domain.Policies.Exceptions;

/// <summary>
/// Exception thrown when a policy's contract holder cannot be found
/// </summary>
public class PolicyContractHolderNotFoundException : DomainException
{
    public PolicyContractHolderNotFoundException(string policyId)
        : base($"Policy {policyId} contract holder not found")
    {
        PolicyId = policyId;
    }

    public PolicyContractHolderNotFoundException(string policyId, Exception innerException)
        : base($"Policy {policyId} contract holder not found", innerException)
    {
        PolicyId = policyId;
    }

    public override string Code => "POLICY_CONTRACT_HOLDER_NOT_FOUND";

    public string PolicyId { get; }
}
