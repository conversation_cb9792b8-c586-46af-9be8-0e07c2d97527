using CoverGo.PoliciesV3.Domain.Common;

namespace CoverGo.PoliciesV3.Domain.Policies.Exceptions;

/// <summary>
/// Exception thrown when trying to perform operations on an issued policy that are not allowed
/// </summary>
public class PolicyIssuedException : DomainException
{
    public PolicyIssuedException(string policyId) : base($"Policy {policyId} shouldn't be issued")
    {
        PolicyId = policyId;
    }

    public PolicyIssuedException(string policyId, Exception innerException)
        : base($"Policy {policyId} shouldn't be issued", innerException)
    {
        PolicyId = policyId;
    }

    public override string Code => "POLICY_ISSUED";

    public string PolicyId { get; }
}
