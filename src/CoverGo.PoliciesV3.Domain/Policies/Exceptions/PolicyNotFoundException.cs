using CoverGo.PoliciesV3.Domain.Common;

namespace CoverGo.PoliciesV3.Domain.Policies.Exceptions;

/// <summary>
/// Exception thrown when a policy cannot be found
/// </summary>
public class PolicyNotFoundException : DomainException
{
    public PolicyNotFoundException(string policyId) : base($"Policy {policyId} not found")
    {
        PolicyId = policyId;
    }

    public PolicyNotFoundException(string policyId, Exception innerException)
        : base($"Policy {policyId} not found", innerException)
    {
        PolicyId = policyId;
    }

    public override string Code => "POLICY_NOT_FOUND";

    public string PolicyId { get; }
}
