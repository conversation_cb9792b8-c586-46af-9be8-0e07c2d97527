using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.BuildingBlocks.Auth.Permissions;
using CoverGo.PoliciesV3.Application.Common.Interfaces;
using CoverGo.PoliciesV3.Application.Services;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;
using CoverGo.PoliciesV3.Domain.CustomFields.Validation;
using CoverGo.PoliciesV3.Domain.Endorsements;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.Policies.Exceptions;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Exceptions;
using Microsoft.Extensions.Logging;
using MediatR;
using LegacyPolicy = CoverGo.Policies.Client.Policy;

namespace CoverGo.PoliciesV3.Application.Features.PolicyMembers.RegisterUpload;

public class RegisterPolicyMemberUploadHandler(
    IRepository<PolicyMemberUpload, PolicyMemberUploadId> policyMemberUploadRepository,
    ILegacyPolicyService legacyPolicyService,
    IPolicyValidationService policyValidationService,
    IPolicyMemberFieldsSchemaProvider policyMemberFieldsSchemaProvider,
    IPermissionValidator permissionValidator,
    IFileSystemService fileSystemService,
    IFileParserFactory fileParserFactory,
    ILogger<RegisterPolicyMemberUploadHandler> logger) : IRequestHandler<RegisterPolicyMemberUploadRequest, RegisterPolicyMemberUploadResponse>
{
    // Constants for permission names to avoid magic strings
    private const string UpdatePoliciesPermission = "updatePolicies";
    private const string WritePoliciesPermission = "writePolicies";
    public async Task<RegisterPolicyMemberUploadResponse> Handle(
        RegisterPolicyMemberUploadRequest request,
        CancellationToken cancellationToken)
    {
        logger.LogInformation("RegisterPolicyMemberUpload started for PolicyId: {PolicyId}, Path: {Path}", request.PolicyId, request.Path);

        await permissionValidator.AuthorizeAsync(request.Identity, new PermissionRequest(UpdatePoliciesPermission, WritePoliciesPermission).WithTargetIds(request.PolicyId.ToString()));

        LegacyPolicy legacyPolicy = await GetValidatedPolicy(request.PolicyId.ToString(), cancellationToken);
        policyValidationService.ValidateCanUpload(legacyPolicy, request.EndorsementId);

        FileParseResult parseResult = await ParseAndValidateFile(request.Path, request.TenantId, request.PolicyId.ToString(), cancellationToken);
        await ValidateSchemaFields(parseResult.HeadersSet, legacyPolicy, request.EndorsementId, cancellationToken);

        var upload = PolicyMemberUpload.Create(new PolicyId(request.PolicyId), request.Path, parseResult.Count, request.EndorsementId);
        await policyMemberUploadRepository.InsertAsync(upload, cancellationToken);

        logger.LogInformation("RegisterPolicyMemberUpload completed for PolicyId: {PolicyId}, Members: {Count}", request.PolicyId, parseResult.Count);

        return new RegisterPolicyMemberUploadResponse
        {
            PolicyMemberUploadId = upload.Id.Value,
            PolicyId = upload.PolicyId.Value,
            EndorsementId = upload.EndorsementId,
            Path = upload.Path,
            MembersCount = upload.MembersCount,
            Status = upload.Status.Value
        };
    }

    private async Task<LegacyPolicy> GetValidatedPolicy(string policyId, CancellationToken cancellationToken)
    {
        LegacyPolicy? legacyPolicy = await legacyPolicyService.GetPolicyById(policyId, cancellationToken);
        return legacyPolicy is null || string.IsNullOrEmpty(legacyPolicy.Id) ? throw new PolicyNotFoundException(policyId) : legacyPolicy;
    }

    private async Task<FileParseResult> ParseAndValidateFile(string path, string tenantId, string policyId, CancellationToken cancellationToken)
    {
        byte[]? fileContent = await fileSystemService.GetFileByPath(path, tenantId, cancellationToken) ?? throw new UploadFileNotFoundException(policyId, path);
        if (fileContent.Length == 0)
            throw new BadFileContentException(BadFileContentErrorCode.EMPTY_FILE, "File content is empty");

        IFileParser parser = fileParserFactory.CreateParser(fileContent);
        FileParseResult parseResult = parser.ParseFile(fileContent) ?? throw new BadFileContentException(BadFileContentErrorCode.INVALID_ROW, "Failed to parse file content");
        return parseResult.Headers is null || parseResult.Headers.Count == 0
            ? throw new BadFileContentException(BadFileContentErrorCode.NO_COLUMN, "No headers found in file")
            : parseResult.Count <= 0
            ? throw new BadFileContentException(BadFileContentErrorCode.NO_MEMBER, "No member data found in file")
            : parseResult;
    }

    private static ProductId ConvertToProductId(CoverGo.Policies.Client.ProductId legacyProductId) =>
        new(legacyProductId.Plan!, legacyProductId.Type!, legacyProductId.Version!);

    private async Task ValidateSchemaFields(
        HashSet<string> uploadFileHeadersSet,
        LegacyPolicy legacyPolicy,
        Guid? endorsementId,
        CancellationToken cancellationToken)
    {
        ValidateProductId(legacyPolicy);

        PolicyMemberFieldsSchema schema = await GetSchema(legacyPolicy, endorsementId, cancellationToken);

        List<(string errorCode, string message)> errors = CollectAllValidationErrors(schema, uploadFileHeadersSet);

        if (errors.Count > 0)
            throw new BadFileContentException(errors);
    }

    private static List<(string errorCode, string message)> CollectAllValidationErrors(PolicyMemberFieldsSchema schema, HashSet<string> uploadFileHeadersSet)
    {
        List<(string errorCode, string message)> errors = new();
        CollectOneOfValidationErrors(schema, uploadFileHeadersSet, errors);
        CollectMandatoryFieldErrors(schema, uploadFileHeadersSet, errors);
        CollectFormulaFieldErrors(schema, uploadFileHeadersSet, errors);
        return errors;
    }

    private static void ValidateProductId(LegacyPolicy legacyPolicy)
    {
        if (legacyPolicy.ProductId is null)
            throw new InvalidOperationException($"Policy {legacyPolicy.Id} is missing ProductId");
        if (string.IsNullOrWhiteSpace(legacyPolicy.ProductId.Plan))
            throw new InvalidOperationException($"Policy {legacyPolicy.Id} ProductId is missing Plan");
        if (string.IsNullOrWhiteSpace(legacyPolicy.ProductId.Type))
            throw new InvalidOperationException($"Policy {legacyPolicy.Id} ProductId is missing Type");
    }

    private async Task<PolicyMemberFieldsSchema> GetSchema(LegacyPolicy legacyPolicy, Guid? endorsementId, CancellationToken cancellationToken)
    {
        PolicyMemberFieldsSchema? schema = await policyMemberFieldsSchemaProvider.GetMemberUploadSchema(
            legacyPolicy.ContractHolder?.Id,
            ConvertToProductId(legacyPolicy.ProductId!),
            endorsementId.HasValue ? new EndorsementId(endorsementId.Value) : null,
            cancellationToken);

        return schema is null || schema.Fields is null
            ? throw new InvalidOperationException("Failed to retrieve valid schema for policy member upload validation")
            : schema;
    }

    private static void CollectOneOfValidationErrors(PolicyMemberFieldsSchema schema, HashSet<string> uploadFileHeadersSet, List<(string errorCode, string message)> errors)
    {
        if (schema.OneOfValidations is null) return;

        foreach (CustomFieldOneOfValidation oneOfValidation in schema.OneOfValidations)
        {
            // Check if any field is present before materializing all labels
            bool hasAnyField = false;
            List<string> fieldLabels = new(oneOfValidation.Validations.Count);

            foreach (var validation in oneOfValidation.Validations)
            {
                string fieldLabel = validation.Field.GetFullLabel();
                fieldLabels.Add(fieldLabel);
                if (uploadFileHeadersSet.Contains(fieldLabel))
                {
                    hasAnyField = true;
                }
            }

            if (!hasAnyField)
            {
                errors.Add((BadFileContentErrorCode.MISSING_ONE_OF_MANDATORY_COLUMNS,
                    $"Missing one of mandatory columns: {string.Join(", ", fieldLabels)}"));
            }
        }
    }

    private static void CollectMandatoryFieldErrors(PolicyMemberFieldsSchema schema, HashSet<string> uploadFileHeadersSet, List<(string errorCode, string message)> errors)
    {
        // Use HashSet for O(1) lookups and avoid multiple enumerations
        HashSet<string> missingHeaders = new();

        // Collect missing mandatory fields (excluding ObjectFieldType and FormulaFieldType)
        foreach (PolicyMemberFieldDefinition field in schema.Fields.Where(x => x is { IsRequired: true, Type: not ObjectFieldType and not FormulaFieldType }))
        {
            string fullLabel = field.GetFullLabel();
            if (!uploadFileHeadersSet.Contains(fullLabel))
            {
                missingHeaders.Add(fullLabel);
            }
        }

        // Collect missing mandatory subfields from ObjectFieldType
        foreach (PolicyMemberFieldDefinition field in schema.Fields.Where(field => field is { IsRequired: true, Type: ObjectFieldType }))
        {
            ObjectFieldType objectFieldType = (ObjectFieldType)field.Type;
            foreach (PolicyMemberFieldDefinition subfield in objectFieldType.InnerFieldDefinitions.Where(subfield => subfield.IsRequired))
            {
                string fullLabel = subfield.GetFullLabel();
                if (!uploadFileHeadersSet.Contains(fullLabel))
                {
                    missingHeaders.Add(fullLabel);
                }
            }
        }

        if (missingHeaders.Count > 0)
        {
            errors.Add((BadFileContentErrorCode.MISSING_MANDATORY_COLUMNS,
                $"Mandatory column(s) are missing: {string.Join(", ", missingHeaders)}"));
        }
    }

    private static void CollectFormulaFieldErrors(PolicyMemberFieldsSchema schema, HashSet<string> uploadFileHeadersSet, List<(string errorCode, string message)> errors)
    {
        // Use HashSet to avoid duplicates and improve performance
        HashSet<string> extraFormulaFields = new();

        foreach (PolicyMemberFieldDefinition field in schema.Fields.Where(x => x is { Type: FormulaFieldType }))
        {
            string fullLabel = field.GetFullLabel();
            if (uploadFileHeadersSet.Contains(fullLabel))
            {
                extraFormulaFields.Add(fullLabel);
            }
        }

        if (extraFormulaFields.Count > 0)
        {
            errors.Add((BadFileContentErrorCode.EXTRA_COLUMN,
                $"Please remove column(s): {string.Join(", ", extraFormulaFields)} in your upload file and reupload"));
        }
    }
}
