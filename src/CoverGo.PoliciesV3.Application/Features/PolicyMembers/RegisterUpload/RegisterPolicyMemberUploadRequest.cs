using CoverGo.PoliciesV3.Application.Common.Interfaces;
using System.Security.Claims;

namespace CoverGo.PoliciesV3.Application.Features.PolicyMembers.RegisterUpload;

public class RegisterPolicyMemberUploadRequest : IRequestWithContextData<RegisterPolicyMemberUploadResponse>
{
    public required Guid PolicyId { get; init; }
    public Guid? EndorsementId { get; init; }
    public required string Path { get; init; }

    // Context data
    public required ClaimsIdentity Identity { get; init; }
    public required string TenantId { get; init; }
}
