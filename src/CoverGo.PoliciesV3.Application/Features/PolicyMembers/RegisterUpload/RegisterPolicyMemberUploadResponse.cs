namespace CoverGo.PoliciesV3.Application.Features.PolicyMembers.RegisterUpload;

public class RegisterPolicyMemberUploadResponse
{
    public required Guid PolicyMemberUploadId { get; init; }
    public required Guid PolicyId { get; init; }
    public Guid? EndorsementId { get; init; }
    public required string Path { get; init; }
    public required int MembersCount { get; init; }
    public required string Status { get; init; }
}
