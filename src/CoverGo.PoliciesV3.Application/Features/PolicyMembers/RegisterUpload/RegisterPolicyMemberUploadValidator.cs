using FluentValidation;

namespace CoverGo.PoliciesV3.Application.Features.PolicyMembers.RegisterUpload;

public class RegisterPolicyMemberUploadValidator : AbstractValidator<RegisterPolicyMemberUploadRequest>
{
    public RegisterPolicyMemberUploadValidator()
    {
        RuleFor(x => x.PolicyId)
            .NotEmpty()
            .WithMessage("PolicyId is required");

        RuleFor(x => x.Path)
            .NotEmpty()
            .WithMessage("Path is required")
            .MaximumLength(500)
            .WithMessage("Path cannot exceed 500 characters");
    }
}
