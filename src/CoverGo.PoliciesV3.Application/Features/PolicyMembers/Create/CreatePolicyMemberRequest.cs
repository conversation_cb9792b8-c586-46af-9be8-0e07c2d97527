using CoverGo.PoliciesV3.Domain.Policies;
using MediatR;

namespace CoverGo.PoliciesV3.Application.Features.PolicyMembers.Create;

public class CreatePolicyMemberRequest : IRequest<CreatePolicyMemberResponse>
{
    public required string MemberId { get; init; }
    public required Guid PolicyId { get; init; }
    public Guid? DependentOfId { get; init; }
    public Guid? IndividualId { get; init; }
    public DateOnly? StartDate { get; init; }
    public DateOnly? EndDate { get; init; }
    public required string PlanId { get; init; }
    public ICollection<PolicyField> Fields { get; init; } = new HashSet<PolicyField>();
}