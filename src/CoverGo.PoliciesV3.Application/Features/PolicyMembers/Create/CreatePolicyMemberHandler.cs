using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMembers;
using MediatR;

namespace CoverGo.PoliciesV3.Application.Features.PolicyMembers.Create;

public class CreatePolicyMemberHandler(
    IRepository<PolicyMember, PolicyMemberId> policyMemberRepository,
    IRepository<Policy, PolicyId> policyRepository) : IRequestHandler<CreatePolicyMemberRequest, CreatePolicyMemberResponse>
{
    public async Task<CreatePolicyMemberResponse> Handle(CreatePolicyMemberRequest request, CancellationToken cancellationToken)
    {
        // Ensure policy exists
        var policyId = new PolicyId(request.PolicyId);
        Policy policy = await policyRepository.FindByIdAsync(policyId, cancellationToken) ?? throw new InvalidOperationException($"Policy with {nameof(PolicyId)} {policyId.Value} not found.");

        // Create policy member
        PolicyMember policyMember = policy.AddPolicyMember(
            request.MemberId,
            request.StartDate,
            request.EndDate,
            request.PlanId,
            request.DependentOfId.HasValue ? new PolicyMemberId(request.DependentOfId.Value) : null,
            request.IndividualId,
            request.Fields
        );

        // Save to repository
        await policyMemberRepository.InsertAsync(policyMember, cancellationToken);

        // Return response
        return new CreatePolicyMemberResponse
        {
            PolicyMemberId = policyMember.Id.Value,
            MemberId = policyMember.MemberId,
            PolicyId = policyId.Value
        };
    }
}