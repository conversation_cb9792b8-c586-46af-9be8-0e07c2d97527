using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.PoliciesV3.Domain.Policies;
using MediatR;

namespace CoverGo.PoliciesV3.Application.Features.Policies.Create;

public class CreatePolicyHandler(
    IRepository<Policy, PolicyId> policyRepository) : IRequestHandler<CreatePolicyRequest, CreatePolicyResponse>
{
    public async Task<CreatePolicyResponse> Handle(CreatePolicyRequest request, CancellationToken cancellationToken)
    {
        string policyNumber = Guid.NewGuid().ToString("N").Substring(0, 8).ToUpperInvariant();

        // Create policy domain object
        var policy = Policy.Create(
            policyNumber,
            request.StartDate!.Value,
            request.EndDate!.Value,
            request.ProductId,
            contractHolderId: null,
            request.Fields
        );

        // Save to repository
        await policyRepository.InsertAsync(policy, cancellationToken);

        // Return response
        return new CreatePolicyResponse
        {
            PolicyId = policy.Id.Value,
            OriginalPolicyNumber = policy.OriginalPolicyNumber
        };
    }
}