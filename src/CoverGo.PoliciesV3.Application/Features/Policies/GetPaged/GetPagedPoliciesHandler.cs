using CoverGo.PoliciesV3.Application.Common;
using CoverGo.PoliciesV3.Domain.Policies;

namespace CoverGo.PoliciesV3.Application.Features.Policies.GetPaged;

public class GetPagedPoliciesHandler(
    IPaginatedRepository<Policy, PolicyId> policyRepository)
{
    public async Task<GetPagedPoliciesResponse> Handle(GetPagedPoliciesRequest request, CancellationToken cancellationToken)
    {
        // Query the repository with pagination
        PageResult<Policy> pageResult = await policyRepository.GetPagedAsync(
            x => (string.IsNullOrWhiteSpace(request.OriginalPolicyNumber) || x.OriginalPolicyNumber == request.OriginalPolicyNumber)
                && (!request.StartDateFrom.HasValue || x.StartDate >= request.StartDateFrom)
                && (!request.StartDateTo.HasValue || x.StartDate <= request.StartDateTo)
                && (!request.EndDateFrom.HasValue || x.EndDate >= request.EndDateFrom)
                && (!request.EndDateTo.HasValue || x.EndDate <= request.EndDateTo),
            request.Skip,
            request.Take,
            request.SortBy,
            request.SortDirection,
            cancellationToken);

        return new GetPagedPoliciesResponse(
            pageResult.Items.Select(x => new PolicyDto
            {
                PolicyId = x.Id.Value,
                OriginalPolicyNumber = x.OriginalPolicyNumber,
                StartDate = x.StartDate,
                EndDate = x.EndDate,
                IsIssued = x.IsIssued,
                IssueDate = x.IssueDate,
                Status = x.Status.Value
            }),
            pageResult.TotalCount,
            request.Skip,
            request.Take);
    }
}