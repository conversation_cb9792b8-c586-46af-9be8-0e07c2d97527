using CoverGo.PoliciesV3.Application.Common;

namespace CoverGo.PoliciesV3.Application.Features.Policies.GetPaged;

public class GetPagedPoliciesResponse(IEnumerable<PolicyDto> items, int totalCount, int pageNumber, int pageSize)
    : PageResult<PolicyDto>(items, totalCount, pageNumber, pageSize)
{
}

public class PolicyDto
{
    public required Guid PolicyId { get; set; }
    public required string OriginalPolicyNumber { get; set; }
    public DateOnly? StartDate { get; set; }
    public DateOnly? EndDate { get; set; }
    public bool IsIssued { get; set; }
    public DateTime? IssueDate { get; set; }
    public required string Status { get; set; }
}