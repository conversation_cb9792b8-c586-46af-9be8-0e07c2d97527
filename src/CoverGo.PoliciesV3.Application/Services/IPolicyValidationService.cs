using CoverGo.PoliciesV3.Domain.Policies.Exceptions;
using CoverGo.PoliciesV3.Domain.Endorsements.Exceptions;
using LegacyPolicy = CoverGo.Policies.Client.Policy;

namespace CoverGo.PoliciesV3.Application.Services;

/// <summary>
/// High-performance policy validation service that works directly with legacy policy data
/// without creating full domain entities
/// </summary>
public interface IPolicyValidationService
{
    /// <summary>
    /// Validates if a policy can accept member uploads using legacy policy data
    /// This is a lightweight validation that doesn't create domain entities
    /// </summary>
    /// <param name="legacyPolicy">Policy data from legacy service</param>
    /// <param name="endorsementId">Optional endorsement ID</param>
    /// <exception cref="PolicyIssuedException">Thrown when policy is issued</exception>
    /// <exception cref="PolicyContractHolderNotFoundException">Thrown when contract holder is required but not found</exception>
    /// <exception cref="EndorsementNotFoundException">Thrown when endorsement is not found</exception>
    /// <exception cref="EndorsementCanNotBeChangedException">Thrown when endorsement cannot be changed</exception>
    void ValidateCanUpload(LegacyPolicy legacyPolicy, Guid? endorsementId = null);
}

/// <summary>
/// High-performance implementation that replicates domain validation logic
/// without the overhead of creating domain entities
/// </summary>
public class PolicyValidationService : IPolicyValidationService
{
    public void ValidateCanUpload(LegacyPolicy legacyPolicy, Guid? endorsementId = null)
    {
        ArgumentNullException.ThrowIfNull(legacyPolicy);

        if (string.IsNullOrEmpty(legacyPolicy.Id))
            throw new ArgumentException("Policy ID cannot be null or empty", nameof(legacyPolicy));

        if (endorsementId is null)
        {
            ValidateCanChangeMembers(legacyPolicy);
        }
        else
        {
            ValidateCanChangeMembersViaMovement(legacyPolicy, endorsementId.Value);
        }
    }

    /// <summary>
    /// Validates policy can have members changed (non-endorsement scenarios)
    /// Equivalent to Policy.EnsureCanChangeMembers()
    /// </summary>
    private static void ValidateCanChangeMembers(LegacyPolicy legacyPolicy)
    {
        if (legacyPolicy.IsIssued)
        {
            throw new PolicyIssuedException(legacyPolicy.Id!);
        }
    }

    /// <summary>
    /// Validates policy can have members changed via endorsement
    /// Equivalent to Policy.EnsureCanChangeMembersViaMovement()
    /// </summary>
    private static void ValidateCanChangeMembersViaMovement(LegacyPolicy legacyPolicy, Guid endorsementId)
    {
        // Check contract holder
        if (string.IsNullOrEmpty(legacyPolicy.ContractHolder?.Id))
        {
            throw new PolicyContractHolderNotFoundException(legacyPolicy.Id!);
        }

        // Find endorsement
        Policies.Client.Endorsement endorsement = (legacyPolicy.Endorsements?.FirstOrDefault(e => 
            Guid.TryParse(e.Id, out Guid id) && id == endorsementId)) ?? throw new EndorsementNotFoundException(legacyPolicy.Id!, endorsementId.ToString());

        // Check if endorsement can be changed
        if (!CanEndorsementBeChanged(endorsement.Status))
        {
            throw new EndorsementCanNotBeChangedException(
                legacyPolicy.Id!, 
                endorsementId.ToString(), 
                endorsement.Status!);
        }
    }

    /// <summary>
    /// Determines if endorsement can be changed based on status
    /// Equivalent to Endorsement.CanBeChanged()
    /// </summary>
    private static bool CanEndorsementBeChanged(string? status) => status switch
    {
        "APPROVED" => false,
        "REJECTED" => false,
        "CANCELED" => false,
        _ => true
    };
}
