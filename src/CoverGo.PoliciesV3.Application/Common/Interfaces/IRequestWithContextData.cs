using MediatR;
using System.Security.Claims;

namespace CoverGo.PoliciesV3.Application.Common.Interfaces;

/// <summary>
/// MediatR request that carries context data (identity, tenantId) within the request itself
/// </summary>
/// <typeparam name="TResponse">The response type</typeparam>
public interface IRequestWithContextData<out TResponse> : IRequest<TResponse>
{
    ClaimsIdentity Identity { get; }
    string TenantId { get; }
}
