namespace CoverGo.PoliciesV3.Application.Common.Interfaces;

/// <summary>
/// Represents the result of parsing a file, containing both headers and content
/// </summary>
public sealed class FileParseResult
{

    /// <summary>
    /// Gets the headers from the file
    /// </summary>
    public required IReadOnlyList<string> Headers { get; init; }

    /// <summary>
    /// Gets the content rows as key-value pairs
    /// </summary>
    public required IReadOnlyList<IReadOnlyDictionary<string, string?>> Contents { get; init; }

    /// <summary>
    /// Gets the headers as a HashSet with case-insensitive comparison for efficient lookups
    /// </summary>
    public HashSet<string> HeadersSet { get; }

    /// <summary>
    /// Gets the number of content rows (excluding header)
    /// </summary>
    public int Count => Contents.Count;

    public FileParseResult()
    {
        HeadersSet = new HashSet<string>(StringComparer.OrdinalIgnoreCase);
    }

    /// <summary>
    /// Initializes the headers set after the Head<PERSON> property is set
    /// This should be called after object initialization
    /// </summary>
    public void InitializeHeadersSet()
    {
        HeadersSet.Clear();
        if (Headers != null)
        {
            foreach (string header in Headers)
            {
                HeadersSet.Add(header);
            }
        }
    }
}

/// <summary>
/// Interface for parsing file content and extracting structured data
/// </summary>
public interface IFileParser
{
    /// <summary>
    /// Extracts headers from the file content
    /// </summary>
    /// <param name="fileContent">The file content as byte array</param>
    /// <returns>Collection of header names</returns>
    IEnumerable<string> GetHeaders(byte[] fileContent);

    /// <summary>
    /// Extracts all content rows from the file as key-value pairs
    /// </summary>
    /// <param name="fileContent">The file content as byte array</param>
    /// <returns>List of dictionaries where keys are headers and values are cell values</returns>
    IList<IDictionary<string, string?>> GetContents(byte[] fileContent);

    /// <summary>
    /// Parses the file content and returns both headers and contents in a single operation
    /// This method is optimized to avoid duplicate parsing operations
    /// </summary>
    /// <param name="fileContent">The file content as byte array</param>
    /// <returns>FileParseResult containing headers, contents, and optimized header lookup</returns>
    FileParseResult ParseFile(byte[] fileContent);
}
