using CoverGo.PoliciesV3.Application.Common.Behaviors;
using CoverGo.PoliciesV3.Application.Features.Policies.GetPaged;
using CoverGo.PoliciesV3.Application.Features.PolicyMembers.GetPaged;
using CoverGo.PoliciesV3.Application.Features.PolicyMembers.RegisterUpload;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.DependencyInjection;

namespace CoverGo.PoliciesV3.Application;

public static class ApplicationRegister
{
    public static IServiceCollection AddApplication(this IServiceCollection services)
    {
        // Register MediatR
        services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(typeof(ApplicationRegister).Assembly));

        // Register all validators from assembly
        services.AddValidatorsFromAssembly(typeof(ApplicationRegister).Assembly);

        // Register MediatR pipeline behaviors
        services.AddTransient(typeof(IPipelineBehavior<,>), typeof(ValidationBehavior<,>));

        // Register query handlers (no validation needed)
        services.AddScoped<GetPagedPoliciesHandler>();
        services.AddScoped<GetPagedPolicyMembersHandler>();

        return services;
    }
}
