using CoverGo.PoliciesV3.Application.Features.PolicyMembers.RegisterUpload;
using CoverGo.PoliciesV3.Domain.Policies.Exceptions;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Exceptions;
using CoverGo.PoliciesV3.Domain.Endorsements.Exceptions;
using System.Security.Claims;
using MediatR;

namespace CoverGo.PoliciesV3.Api.GraphQL.PolicyMembers.RegisterUpload;

[ExtendObjectType(typeof(Mutation))]
public class RegisterPolicyMemberUploadMutation
{
    [GraphQLDescription("Register a policy member upload")]
    [UseMutationConvention(PayloadFieldName = "result")]
    [Error<PolicyNotFoundException>]
    [Error<PolicyIssuedException>]
    [Error<PolicyContractHolderNotFoundException>]
    [Error<UploadFileNotFoundException>]
    [Error<BadFileContentException>]
    [Error<EndorsementNotFoundException>]
    public async Task<RegisterPolicyMemberUploadResponse> RegisterPolicyMemberUpload(
        string policyId,
        string? endorsementId,
        string path,
        [GlobalState] string tenantId,
        [GlobalState] ClaimsIdentity identity,
        [Service] IMediator mediator,
        CancellationToken cancellationToken = default)
    {
        var request = new RegisterPolicyMemberUploadRequest
        {
            PolicyId = Guid.Parse(policyId),
            EndorsementId = !string.IsNullOrEmpty(endorsementId) ? Guid.Parse(endorsementId) : null,
            Path = path,
            Identity = identity,
            TenantId = tenantId
        };

        return await mediator.Send(request, cancellationToken);
    }
}