using CoverGo.BuildingBlocks.Auth.Extensions;
using CoverGo.BuildingBlocks.Bootstrapper.ApiBootstrapper;
using CoverGo.PoliciesV3.Api.GraphQL;
using CoverGo.PoliciesV3.Application;
using CoverGo.PoliciesV3.Infrastructure;
using CoverGo.PoliciesV3.Infrastructure.Common.Constants;
using CoverGo.PoliciesV3.Infrastructure.DataAccess;
using Microsoft.EntityFrameworkCore;

WebApplicationBuilder builder = WebApplication.CreateBuilder(args);
ApiServiceBootstrapper webAppBuilder = ApiServiceBootstrapper.Initialize(builder)
    .WithCoreSetup()
    .WithMultiTenantContext()
    .WithAuthentication()
    .WithLogging()
    .WithMetrics()
    .WithTracing()
    .WithCoreHealthCheck()
    .WithServiceConfiguration(services =>
        {
            services.AddMemoryCache();
            services.AddInfrastructure();
            services.AddApplication();
            services.AddCoverGoAuthorization(builder.Configuration);
            services.AddCoverGoGraphQL();
            
            // Configure header propagation for Authorization and Tenant headers
            services.AddHeaderPropagation(options =>
            {
                options.Headers.Add("Authorization");
                options.Headers.Add("Tenant");
            });
        });

WebApplication app = webAppBuilder.BuildWebApp();

// Add header propagation middleware (must be before any middleware that makes HTTP calls)
app.UseHeaderPropagation();

// Add GraphQL endpoint
app.MapGraphQL();

app.MapGet("/migration", async (IConfiguration configuration) =>
{
    // Get tenant list from configuration
    List<string>? tenants = configuration.GetSection(ConfigurationConstants.Sections.Tenants).Get<List<string>>() ?? [];
    string connectionStringTemplate = configuration.GetConnectionString(ConfigurationConstants.ConnectionStrings.DefaultConnection)!;

    foreach (string tenant in tenants)
    {
        // Replace {tenant} in the connection string
        string connectionString = connectionStringTemplate.Replace(ConfigurationConstants.TemplatePlaceholders.Tenant, tenant);

        // Create DbContext options
        var optionsBuilder = new DbContextOptionsBuilder<ApplicationDbContext>();
        optionsBuilder.UseNpgsql(connectionString);

        // Apply migrations
        await using var dbContext = new ApplicationDbContext(optionsBuilder.Options);
        dbContext.Database.Migrate();
    }
});

app.Run();

// This is to make autogenerated class Program public. It is required to allow it to be used for WebApplicationFactory<Program>.
// https://learn.microsoft.com/en-us/aspnet/core/test/integration-tests?view=aspnetcore-8.0#basic-tests-with-the-default-webapplicationfactory
public partial class Program { }