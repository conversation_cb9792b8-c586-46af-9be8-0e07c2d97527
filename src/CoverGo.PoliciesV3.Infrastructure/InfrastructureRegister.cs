using CoverGo.FeatureManagement;
using CoverGo.PoliciesV3.Application.Common.Interfaces;
using CoverGo.PoliciesV3.Application.Services;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMembers;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Infrastructure.DataAccess;
using CoverGo.PoliciesV3.Infrastructure.DataAccess.PostgreSql;
using CoverGo.PoliciesV3.Infrastructure.FileParser;
using CoverGo.PoliciesV3.Infrastructure.Providers;
using CoverGo.PoliciesV3.Infrastructure.Repositories;
using CoverGo.PoliciesV3.Infrastructure.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace CoverGo.PoliciesV3.Infrastructure;

public static class InfrastructureRegister
{
    public static IServiceCollection AddInfrastructure(this IServiceCollection services)
    {
        services.AddDatabase<ApplicationDbContext>();
        services.AddPostgreSqlRepository<ApplicationDbContext, Policy, PolicyId>();
        services.AddPostgreSqlRepository<ApplicationDbContext, PolicyMember, PolicyMemberId>();
        services.AddPostgreSqlRepository<ApplicationDbContext, PolicyMemberUpload, PolicyMemberUploadId>();

        // Register file parsing services
        services.AddSingleton<IFileParserFactory, FileParserFactory>();

        // Register schema services
        services.AddScoped<IPolicyMemberFieldsSchemaRepository, PolicyMemberFieldsSchemaRepository>();
        services.AddScoped<IPolicyMemberFieldsSchemaProvider, PolicyMemberFieldsSchemaProvider>();

        // Register feature management
        services.AddSingleton<IMultiTenantFeatureManager, MultiTenantFeatureManager>();

        // Register external service clients with header propagation
        services.AddExternalServices();

        return services;
    }

    private static IServiceCollection AddExternalServices(this IServiceCollection services)
    {
        // Register service interfaces with their implementations
        services.AddScoped<IUsersService, UsersService>();
        services.AddScoped<ICasesService, CasesService>();
        services.AddScoped<IFileSystemService, FileSystemService>();
        services.AddScoped<ILegacyPolicyService, LegacyPolicyService>();
        services.AddScoped<IPolicyValidationService, PolicyValidationService>();
        services.AddScoped<IProductService, ProductService>();

        // Register HttpClient for each service with header propagation
        services.AddExternalServiceWithHttpClient<UsersService>("users");
        services.AddExternalServiceWithHttpClient<CasesService>("cases");
        services.AddExternalServiceWithHttpClient<FileSystemService>("filesystem");
        services.AddExternalServiceWithHttpClient<LegacyPolicyService>("policies");
        services.AddExternalServiceWithHttpClient<ProductService>("products");



        return services;
    }

    /// <summary>
    /// Registers a service with HttpClient configured from serviceUrls configuration
    /// Header propagation is configured at the application level in Program.cs
    /// </summary>
    /// <typeparam name="TService">The service type to register</typeparam>
    /// <param name="services">The service collection</param>
    /// <param name="serviceUrlKey">The key in serviceUrls configuration (e.g., "users", "cases")</param>
    private static IServiceCollection AddExternalServiceWithHttpClient<TService>(
        this IServiceCollection services,
        string serviceUrlKey)
        where TService : class
    {
        services.AddHttpClient<TService>((serviceProvider, client) =>
        {
            IConfiguration configuration = serviceProvider.GetRequiredService<IConfiguration>();
            string? baseUrl = configuration[$"serviceUrls:{serviceUrlKey}"];
            if (!string.IsNullOrEmpty(baseUrl))
                client.BaseAddress = new Uri(baseUrl);
        });

        return services;
    }
}