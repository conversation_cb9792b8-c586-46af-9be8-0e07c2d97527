﻿using CoverGo.FileSystem.Client;
using CoverGo.Multitenancy;
using CoverGo.PoliciesV3.Application.Services;
using Microsoft.Extensions.Logging;

namespace CoverGo.PoliciesV3.Infrastructure.Services;

internal class FileSystemService(HttpClient httpClient, TenantId tenantId, ILogger<FileSystemService> logger) : IFileSystemService
{
    readonly FileSystemClient _fileSystemClient = new(httpClient);
    readonly TenantId _tenantId = tenantId;
    private readonly ILogger<FileSystemService> _logger = logger;

    public async Task<byte[]?> GetFileByPath(string path, CancellationToken cancellationToken)
    {
        ResultOfByteOf? getResult = await _fileSystemClient.FileSystem_GetAsync(_tenantId.Value, null, new GetFileCommand { Key = path }, cancellationToken);
        if (!getResult?.IsSuccess ?? true)
            _logger.LogError("Failed to get file for path {path}. ||| Message: {message}", path, string.Join(',', getResult?.Errors ?? []));
        return getResult?.Value;
    }

    public async Task<byte[]?> GetFileByPath(string path, string? tenantId, CancellationToken cancellationToken)
    {
        // Use provided tenantId if available, otherwise use the injected one
        string effectiveTenantId = tenantId ?? _tenantId.Value;
        ResultOfByteOf? getResult = await _fileSystemClient.FileSystem_GetAsync(effectiveTenantId, null, new GetFileCommand { Key = path }, cancellationToken);
        if (!getResult?.IsSuccess ?? true)
            _logger.LogError("Failed to get file for path {path} with tenantId {tenantId}. ||| Message: {message}", path, effectiveTenantId, string.Join(',', getResult?.Errors ?? []));
        return getResult?.Value;
    }
}
