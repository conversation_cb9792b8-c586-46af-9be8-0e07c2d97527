﻿using CoverGo.Cases.Client.Rest;
using CoverGo.Multitenancy;
using CoverGo.PoliciesV3.Application.Services;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Linq;

namespace CoverGo.PoliciesV3.Infrastructure.Services;

internal class CasesService(HttpClient httpClient, TenantId tenantId, ILogger<CasesService> logger) : ICasesService
{
    readonly CasesRestClient _casesClient = new(httpClient);
    readonly TenantId _tenantId = tenantId;
    private readonly ILogger<CasesService> _logger = logger;

    public async Task<JToken?> GetMemberDataSchema(CancellationToken cancellationToken)
    {
        DataSchema? schema = (await _casesClient.DataSchemas_GetAllAsync(_tenantId.Value, new()
        {
            Type = "member"
        }, cancellationToken))?.FirstOrDefault();
        return schema?.Schema as JToken;
    }
}
