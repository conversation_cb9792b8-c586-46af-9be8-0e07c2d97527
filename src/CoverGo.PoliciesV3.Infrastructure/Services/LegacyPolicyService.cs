﻿using CoverGo.Multitenancy;
using CoverGo.Policies.Client;
using CoverGo.PoliciesV3.Application.Services;

namespace CoverGo.PoliciesV3.Infrastructure.Services;

internal class LegacyPolicyService(HttpClient httpClient, TenantId tenantId) : ILegacyPolicyService
{
    readonly PoliciesClient _policiesRestClient = new(httpClient);
    readonly TenantId _tenantId = tenantId;

    public async Task<Policy?> GetPolicyById(string policyId, CancellationToken cancellationToken)
    {
        Policy? policy = await _policiesRestClient.Policy_GetPolicyAsync(_tenantId.Value, policyId, null, cancellationToken);
        return policy;
    }
}
