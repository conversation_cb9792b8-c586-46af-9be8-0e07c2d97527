using CoverGo.PoliciesV3.Application.Services;
using CoverGo.Products.Client;
using CoverGo.PoliciesV3.Infrastructure.Common.Helpers;
using GraphQL.Client.Http;
using GraphQL.Client.Serializer.Newtonsoft;
using Microsoft.Extensions.Logging;

namespace CoverGo.PoliciesV3.Infrastructure.Services;

internal class ProductService : IProductService
{
    private readonly GraphQLHttpClient _productsGraphqlClient;
    private readonly ILogger<ProductService> _logger;

    public ProductService(HttpClient httpClient, ILogger<ProductService> logger)
    {
        _productsGraphqlClient = new GraphQLHttpClient(new GraphQLHttpClientOptions(), new NewtonsoftJsonSerializer(), httpClient);
        _logger = logger;
    }

    public async Task<string?> GetProductMemberSchema(ProductId productId, CancellationToken cancellationToken)
    {
        products_Product? product = await GetProductById(productId, cancellationToken);
        if (product is null)
        {
            _logger.LogError("Product with ID {ProductId} not found.", productId);
            return null;
        }
        products_Script? pricingScript = product.scripts.FirstOrDefault(s => s.type == products_ScriptTypeEnum.PRICING_STANDARD)
                 ?? product.scripts.FirstOrDefault(s => s.type == products_ScriptTypeEnum.PRICING);
        if (pricingScript is null || pricingScript.inputSchema is null)
        {
            _logger.LogError("PricingScript with input schema for product with ID {ProductId} not found.", productId);
            return null;
        }
        return pricingScript.inputSchema;
    }

    async Task<products_Product> GetProductById(ProductId productId, CancellationToken cancellationToken)
    {
        string query = new QueryBuilder().product(
            new QueryBuilder.productArgs(new() { plan = productId.Plan, version = productId.Version, type = productId.Type }),
            new products_ProductBuilder()
                        .id(new products_ProductIdBuilder().WithAllFields())
                        .scripts(new products_ProductBuilder.scriptsArgs(new products_ScriptWhereInput
                        {
                            or = [new() { type = products_ScriptTypeEnum.PRICING }, new() { type = products_ScriptTypeEnum.PRICING_STANDARD }]
                        }), new products_ScriptBuilder().WithAllFields())).Build();

        return await _productsGraphqlClient.SendQueryAndEnsureAsync<products_Product>(query, cancellationToken);
    }
}

