﻿<Project Sdk="Microsoft.NET.Sdk">
  <ItemGroup>
    <PackageReference Include="CoverGo.BuildingBlocks.DataAccess" />
    <PackageReference Include="CoverGo.FeatureManagement" />
    <PackageReference Include="CoverGo.Users.Client" />
    <PackageReference Include="GraphQL.Client" />
    <PackageReference Include="GraphQL.Client.Serializer.Newtonsoft" />
    <PackageReference Include="Humanizer.Core" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" />
    <PackageReference Include="NPOI" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\CoverGo.PoliciesV3.Application\CoverGo.PoliciesV3.Application.csproj" />
    <ProjectReference Include="..\CoverGo.PoliciesV3.Domain\CoverGo.PoliciesV3.Domain.csproj" />
    <PackageReference Include="CoverGo.Cases.Client.Rest" />
    <PackageReference Include="CoverGo.FileSystem.Client" />
    <PackageReference Include="CoverGo.Policies.Client" />
    <PackageReference Include="CoverGo.Products.Client" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Migrations/" />
  </ItemGroup>
</Project>
