using System.Text.Json;
using System.Text.Json.Serialization;

namespace CoverGo.PoliciesV3.Infrastructure.Common.Helpers;

/// <summary>
/// JSON serialization helpers for consistent database storage formatting.
/// </summary>
public static class JsonSerializationHelpers
{
    /// <summary>
    /// Snake case naming policy for JSON serialization to maintain consistency with database naming conventions.
    /// </summary>
    public sealed class SnakeCaseNamingPolicy : JsonNamingPolicy
    {
        /// <summary>
        /// Converts property names from PascalCase/camelCase to snake_case.
        /// </summary>
        /// <param name="name">The property name to convert</param>
        /// <returns>The snake_case equivalent</returns>
        public override string ConvertName(string name) => string.IsNullOrEmpty(name) ? name : DatabaseNamingHelpers.ConvertToSnakeCase(name);
    }

    /// <summary>
    /// JsonSerializerOptions configured for database storage with snake_case property naming and enum string conversion.
    /// </summary>
    public static readonly JsonSerializerOptions DatabaseJsonOptions = CreateDatabaseJsonOptions();

    private static JsonSerializerOptions CreateDatabaseJsonOptions()
    {
        var options = new JsonSerializerOptions
        {
            PropertyNamingPolicy = new SnakeCaseNamingPolicy(),
            WriteIndented = false, // Compact JSON for database storage
            PropertyNameCaseInsensitive = true // Allow flexible deserialization
        };

        options.Converters.Add(new JsonStringEnumConverter(new SnakeCaseNamingPolicy()));

        return options;
    }
}
