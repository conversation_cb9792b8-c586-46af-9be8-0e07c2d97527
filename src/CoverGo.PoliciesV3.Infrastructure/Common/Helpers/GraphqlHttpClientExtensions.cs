using GraphQL;
using GraphQL.Client.Http;
using Newtonsoft.Json.Linq;

namespace CoverGo.PoliciesV3.Infrastructure.Common.Helpers;

static class GraphQlHttpClientExtensions
{
    public static async Task<TResponse> SendMutationAndEnsureAsync<TResponse>(this GraphQLHttpClient client, string mutation, CancellationToken? cancellationToken = null) where TResponse : class
    {
        GraphQLResponse<JToken> response = await client.SendMutationAsync<JToken>(new GraphQLHttpRequest(mutation), cancellationToken ?? CancellationToken.None);
        Validate(response);
        return response.Data.First?.First?.ToObject<TResponse>() ?? throw new ArgumentException("Cannot deserialize result");
    }

    public static async Task<TResponse> SendQueryAndEnsureAsync<TResponse>(this GraphQLHttpClient client, string query, CancellationToken? cancellationToken = null) where TResponse : class
    {
        GraphQLResponse<JToken> response = await client.SendQueryAsync<JToken>(new GraphQLHttpRequest(query), cancellationToken ?? CancellationToken.None);
        Validate(response);
        return response.Data.First?.First?.ToObject<TResponse>() ?? throw new ArgumentException("Cannot deserialize result");
    }

    static void Validate<TResponse>(GraphQLResponse<TResponse> response)
    {
        if (response.Errors?.Length > 0)
        {
            GraphQLError error = response.Errors[0];
            string exceptionMessage = error.Message;
            if (error.Extensions?.TryGetValue("message", out var extraMessage) == true)
            {
                exceptionMessage += Environment.NewLine + extraMessage;
            }
            throw new InvalidOperationException(exceptionMessage);
        }
    }
}