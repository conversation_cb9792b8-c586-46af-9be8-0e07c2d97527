using System.Text;

namespace CoverGo.PoliciesV3.Infrastructure.Common.Helpers;

/// <summary>
/// Provides compile-time safe database naming helpers that eliminate magic strings
/// and automatically generate consistent PostgreSQL snake_case names.
/// </summary>
public static class DatabaseNamingHelpers
{
    #region Core Naming Methods

    /// <summary>
    /// Converts PascalCase to snake_case for database naming conventions.
    /// </summary>
    /// <param name="input">The PascalCase string to convert</param>
    /// <returns>The snake_case equivalent</returns>
    /// <exception cref="ArgumentNullException">Thrown when input is null or whitespace</exception>
    public static string ConvertToSnakeCase(string input)
    {
        if (string.IsNullOrWhiteSpace(input))
            throw new ArgumentNullException(nameof(input));

        var builder = new StringBuilder();
        for (int i = 0; i < input.Length; i++)
        {
            char current = input[i];
            if (char.IsUpper(current) && i > 0)
                builder.Append('_');
            builder.Append(char.<PERSON>(current));
        }

        return builder.ToString();
    }

    /// <summary>
    /// Gets the table name for an entity type using snake_case convention.
    /// </summary>
    public static string GetTableName<TEntity>() => ConvertToSnakeCase(typeof(TEntity).Name);

    /// <summary>
    /// Gets the table name for an entity type using snake_case convention.
    /// </summary>
    public static string GetTableName(Type entityType) => ConvertToSnakeCase(entityType.Name);

    /// <summary>
    /// Gets the column name for a property using snake_case convention.
    /// </summary>
    public static string GetColumnName<TEntity>(string propertyName) => ConvertToSnakeCase(propertyName);

    #endregion

    #region Index Naming

    /// <summary>
    /// Generates an index name for a single column.
    /// Pattern: ix_{table_name}_{column_name}
    /// </summary>
    public static string GetIndexName<TEntity>(string propertyName)
    {
        string tableName = GetTableName<TEntity>();
        string columnName = GetColumnName<TEntity>(propertyName);
        return TruncateIdentifier($"ix_{tableName}_{columnName}");
    }

    /// <summary>
    /// Generates an index name for multiple columns.
    /// Pattern: ix_{table_name}_{column1}_{column2}_{columnN}
    /// </summary>
    public static string GetCompositeIndexName<TEntity>(params string[] propertyNames)
    {
        string tableName = GetTableName<TEntity>();
        IEnumerable<string> columnNames = propertyNames.Select(p => GetColumnName<TEntity>(p));
        return TruncateIdentifier($"ix_{tableName}_{string.Join("_", columnNames)}");
    }

    /// <summary>
    /// Generates a unique index name for a single column.
    /// Pattern: uix_{table_name}_{column_name}
    /// </summary>
    public static string GetUniqueIndexName<TEntity>(string propertyName)
    {
        string tableName = GetTableName<TEntity>();
        string columnName = GetColumnName<TEntity>(propertyName);
        return TruncateIdentifier($"uix_{tableName}_{columnName}");
    }

    #endregion

    #region Foreign Key Naming

    /// <summary>
    /// Generates a foreign key constraint name.
    /// Pattern: fk_{child_table}_{parent_table}_{foreign_key_column}
    /// </summary>
    public static string GetForeignKeyName<TChildEntity, TParentEntity>(string foreignKeyPropertyName)
    {
        string childTable = GetTableName<TChildEntity>();
        string parentTable = GetTableName<TParentEntity>();
        string foreignKeyColumn = GetColumnName<TChildEntity>(foreignKeyPropertyName);
        return TruncateIdentifier($"fk_{childTable}_{parentTable}_{foreignKeyColumn}");
    }

    /// <summary>
    /// Generates a self-referencing foreign key constraint name.
    /// Pattern: fk_{table}_{table}_{foreign_key_column}
    /// </summary>
    public static string GetSelfReferencingForeignKeyName<TEntity>(string foreignKeyPropertyName) => GetForeignKeyName<TEntity, TEntity>(foreignKeyPropertyName);

    #endregion

    #region Primary Key Naming

    /// <summary>
    /// Generates a primary key constraint name.
    /// Pattern: pk_{table_name}
    /// </summary>
    public static string GetPrimaryKeyName<TEntity>()
    {
        string tableName = GetTableName<TEntity>();
        return TruncateIdentifier($"pk_{tableName}");
    }

    #endregion

    #region Check Constraint Naming

    /// <summary>
    /// Generates a check constraint name.
    /// Pattern: ck_{table_name}_{constraint_description}
    /// </summary>
    public static string GetCheckConstraintName<TEntity>(string constraintDescription)
    {
        string tableName = GetTableName<TEntity>();
        string description = ConvertToSnakeCase(constraintDescription);
        return TruncateIdentifier($"ck_{tableName}_{description}");
    }

    #endregion

    #region Utility Methods

    /// <summary>
    /// Truncates a database identifier to PostgreSQL's maximum length (63 characters)
    /// while preserving readability by keeping the suffix intact.
    /// </summary>
    public static string TruncateIdentifier(string identifier, int maxLength = 63)
    {
        if (identifier.Length <= maxLength)
            return identifier;

        // Find the last underscore to preserve the suffix
        int lastUnderscoreIndex = identifier.LastIndexOf('_');
        if (lastUnderscoreIndex == -1)
        {
            // No underscore found, just truncate
            return identifier[..maxLength];
        }

        string suffix = identifier[lastUnderscoreIndex..];
        string prefix = identifier[..lastUnderscoreIndex];

        int availableLength = maxLength - suffix.Length;
        if (availableLength <= 0)
        {
            // Suffix is too long, just truncate the whole thing
            return identifier[..maxLength];
        }

        return string.Concat(prefix.AsSpan(0, Math.Min(prefix.Length, availableLength)), suffix);
    }

    /// <summary>
    /// Converts PascalCase to kebab-case for URL-friendly naming conventions.
    /// </summary>
    /// <param name="input">The PascalCase string to convert</param>
    /// <returns>The kebab-case equivalent</returns>
    /// <exception cref="ArgumentNullException">Thrown when input is null or whitespace</exception>
    public static string ConvertToKebabCase(string input)
    {
        if (string.IsNullOrWhiteSpace(input))
            throw new ArgumentNullException(nameof(input));

        var builder = new StringBuilder();
        for (int i = 0; i < input.Length; i++)
        {
            char current = input[i];
            if (char.IsUpper(current) && i > 0)
                builder.Append('-');
            builder.Append(char.ToLower(current));
        }

        return builder.ToString();
    }

    #endregion
}