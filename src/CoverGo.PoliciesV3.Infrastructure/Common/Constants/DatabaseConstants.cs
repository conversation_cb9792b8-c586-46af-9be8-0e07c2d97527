namespace CoverGo.PoliciesV3.Infrastructure.Common.Constants;

/// <summary>
/// Constants for database column types to avoid magic strings.
/// These constants can be used across different database providers.
/// </summary>
public static class DatabaseConstants
{
    /// <summary>
    /// PostgreSQL-specific column types
    /// </summary>
    public static class PostgreSql
    {
        /// <summary>
        /// Column types for PostgreSQL database
        /// </summary>
        public static class ColumnTypes
        {
            public const string Uuid = "uuid";
            public const string Boolean = "boolean";
            public const string Integer = "integer";
            public const string Varchar255 = "varchar(255)";
            public const string Varchar100 = "varchar(100)";
            public const string Varchar50 = "varchar(50)";
            public const string Text = "text";
            public const string Date = "date";
            public const string Timestamp = "timestamp";
            public const string TimestampWithTimeZone = "timestamp with time zone";
            public const string Jsonb = "jsonb";
            public const string Xid = "xid";
        }

        /// <summary>
        /// PostgreSQL system column names
        /// </summary>
        public static class SystemColumns
        {
            public const string Xmin = "xmin";
        }

        /// <summary>
        /// Property names that map to PostgreSQL system columns
        /// </summary>
        public static class SystemColumnPropertyNames
        {
            public const string RowVersion = "RowVersion";
        }
    }
}
