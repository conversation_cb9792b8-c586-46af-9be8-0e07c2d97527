namespace CoverGo.PoliciesV3.Infrastructure.Common.Constants;

/// <summary>
/// Constants for HTTP client names to avoid magic strings.
/// Centralizes all HTTP client-related string literals.
/// </summary>
public static class HttpClientConstants
{
    /// <summary>
    /// HTTP client names for external services
    /// </summary>
    public static class ClientNames
    {
        public const string FileSystem = "fileSystem";
    }
}
