using CoverGo.PoliciesV3.Application.Common.Interfaces;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Exceptions;
using System.Text;

namespace CoverGo.PoliciesV3.Infrastructure.FileParser;

/// <summary>
/// CSV file parser implementation with caching support
/// </summary>
public sealed class CsvFileParser : IFileParser
{
    private const char Delimiter = ',';
    private FileParseResult? _cachedResult;
    private byte[]? _cachedFileContent;

    public IEnumerable<string> GetHeaders(byte[] fileContent) => ParseFile(fileContent).Headers;

    public IList<IDictionary<string, string?>> GetContents(byte[] fileContent) => [.. ParseFile(fileContent).Contents.Select(dict => (IDictionary<string, string?>)dict)];

    public FileParseResult ParseFile(byte[] fileContent)
    {
        // Check if we have cached result for the same file content
        if (_cachedResult != null && _cachedFileContent != null && _cachedFileContent.SequenceEqual(fileContent))
        {
            return _cachedResult;
        }

        try
        {
            string[] rows = GetRows(fileContent);
            if (rows.Length == 0)
                throw new BadFileContentException(BadFileContentErrorCode.EMPTY_FILE, "File content is empty");

            string[] headers = rows[0].Split(Delimiter, StringSplitOptions.TrimEntries);
            var contents = new List<IReadOnlyDictionary<string, string?>>();

            for (int rowIndex = 1; rowIndex < rows.Length; rowIndex++)
            {
                string[] data = rows[rowIndex].Split(Delimiter, StringSplitOptions.TrimEntries);
                if (data.Length != headers.Length)
                    throw new BadFileContentException(BadFileContentErrorCode.INVALID_ROW,
                        $"Invalid content at row index {rowIndex}. Expected {headers.Length} columns, got {data.Length}");

                var dict = new Dictionary<string, string?>();
                for (int i = 0; i < headers.Length; i++)
                {
                    dict[headers[i]] = string.IsNullOrWhiteSpace(data[i]) ? null : data[i];
                }
                contents.Add(dict);
            }

            var result = new FileParseResult
            {
                Headers = [.. headers],
                Contents = contents
            };

            // Initialize the headers set for optimized lookups
            result.InitializeHeadersSet();

            // Cache the result
            _cachedResult = result;
            _cachedFileContent = fileContent;

            return result;
        }
        catch (BadFileContentException)
        {
            throw;
        }
        catch (Exception exception)
        {
            throw new BadFileContentException(BadFileContentErrorCode.INVALID_ROW,
                $"CSV file content is not valid: {exception.Message}", exception);
        }
    }



    private static string[] GetRows(byte[] fileContent) => fileContent == null || fileContent.Length == 0
        ? []
        : Encoding.UTF8.GetString(fileContent).Split('\n', StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries);
}
