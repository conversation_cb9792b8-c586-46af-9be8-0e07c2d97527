using CoverGo.PoliciesV3.Application.Common.Interfaces;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Exceptions;
using Microsoft.Extensions.Logging;

namespace CoverGo.PoliciesV3.Infrastructure.FileParser;

/// <summary>
/// Factory for creating appropriate file parsers based on file content
/// </summary>
public sealed class FileParserFactory(ILogger<FileParserFactory> logger) : IFileParserFactory
{

    /// <summary>
    /// Magic number for XLSX files (ZIP file signature: PK..)
    /// Reference: https://en.wikipedia.org/wiki/List_of_file_signatures
    /// </summary>
    private static readonly byte[] XlsxMagicNumber = [0x50, 0x4B, 0x03, 0x04];

    public IFileParser CreateParser(byte[] fileContent)
    {
        if (fileContent == null || fileContent.Length == 0)
        {
            logger.LogError("Attempted to create parser for empty file content");
            throw new BadFileContentException(BadFileContentErrorCode.EMPTY_FILE, "File content is empty");
        }

        FileType fileType = DetectFileType(fileContent);
        logger.LogDebug("Detected file type: {FileType} for content of {Size} bytes", fileType, fileContent.Length);

        return fileType switch
        {
            FileType.Xlsx => new XlsxFileParser(),
            FileType.Csv => new CsvFileParser(),
            _ => throw new BadFileContentException(BadFileContentErrorCode.UNSUPPORTED_FILE_TYPE, 
                $"Unsupported file type: {fileType}")
        };
    }

    private static FileType DetectFileType(byte[] fileContent)
    {
        if (fileContent.Length >= XlsxMagicNumber.Length && 
            fileContent.Take(XlsxMagicNumber.Length).SequenceEqual(XlsxMagicNumber))
        {
            return FileType.Xlsx;
        }

        // Default to CSV for other file types
        return FileType.Csv;
    }

    private enum FileType
    {
        Csv,
        Xlsx
    }
}
