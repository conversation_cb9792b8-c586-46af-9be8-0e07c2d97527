using CoverGo.PoliciesV3.Application.Common.Interfaces;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Exceptions;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;

namespace CoverGo.PoliciesV3.Infrastructure.FileParser;

/// <summary>
/// Excel XLSX file parser implementation using NPOI library with caching support
/// </summary>
public sealed class XlsxFileParser : IFileParser
{
    private FileParseResult? _cachedResult;
    private byte[]? _cachedFileContent;

    public IEnumerable<string> GetHeaders(byte[] fileContent) => ParseFile(fileContent).Headers;

    public IList<IDictionary<string, string?>> GetContents(byte[] fileContent) => [.. ParseFile(fileContent).Contents.Select(dict => (IDictionary<string, string?>)dict)];

    public FileParseResult ParseFile(byte[] fileContent)
    {
        // Check if we have cached result for the same file content
        if (_cachedResult != null && _cachedFileContent != null && _cachedFileContent.SequenceEqual(fileContent))
        {
            return _cachedResult;
        }

        try
        {
            using XSSFWorkbook workbook = CreateWorkbook(fileContent);
            ISheet sheet = GetFirstSheet(workbook);
            IRow headerRow = sheet.GetRow(sheet.FirstRowNum) ?? throw new BadFileContentException(BadFileContentErrorCode.EMPTY_FILE, "No header row found in Excel file");

            string[] headers = [.. headerRow.Select(GetCellStringValue)];
            var contents = new List<IReadOnlyDictionary<string, string?>>();

            for (int rowIndex = sheet.FirstRowNum + 1; rowIndex <= sheet.LastRowNum; rowIndex++)
            {
                IRow? row = sheet.GetRow(rowIndex);
                if (row == null || IsEmptyRow(row))
                    continue;

                var dict = new Dictionary<string, string?>();
                for (int colIndex = 0; colIndex < headers.Length; colIndex++)
                {
                    string cellValue = GetCellValue(row, colIndex);
                    dict[headers[colIndex]] = string.IsNullOrWhiteSpace(cellValue) ? null : cellValue;
                }
                contents.Add(dict);
            }

            var result = new FileParseResult
            {
                Headers = [.. headers],
                Contents = contents
            };

            // Initialize the headers set for optimized lookups
            result.InitializeHeadersSet();

            // Cache the result
            _cachedResult = result;
            _cachedFileContent = fileContent;

            return result;
        }
        catch (BadFileContentException)
        {
            throw;
        }
        catch (Exception exception)
        {
            throw new BadFileContentException(BadFileContentErrorCode.INVALID_XLSX_FILE,
                $"XLSX file content is not valid: {exception.Message}", exception);
        }
    }



    private static XSSFWorkbook CreateWorkbook(byte[] fileContent) => fileContent == null || fileContent.Length == 0
            ? throw new BadFileContentException(BadFileContentErrorCode.EMPTY_FILE, "File content is empty")
            : new XSSFWorkbook(new MemoryStream(fileContent));

    private static ISheet GetFirstSheet(XSSFWorkbook workbook) => workbook.NumberOfSheets == 0
            ? throw new BadFileContentException(BadFileContentErrorCode.INVALID_XLSX_FILE, "Excel file contains no sheets")
            : workbook.GetSheetAt(0);

    private static string GetCellStringValue(ICell? cell) => cell?.StringCellValue ?? string.Empty;

    private static string GetCellValue(IRow row, int columnIndex)
    {
        ICell? cell = row.GetCell(columnIndex);
        return cell == null
            ? string.Empty
            : cell.CellType switch
            {
                CellType.Numeric => DateUtil.IsCellDateFormatted(cell)
                    ? string.Format("{0:yyyy-MM-dd}", cell.DateCellValue)
                    : cell.NumericCellValue.ToString(),
                CellType.Boolean => cell.BooleanCellValue.ToString(),
                CellType.String => cell.StringCellValue,
                CellType.Formula => GetFormulaValue(cell),
                CellType.Blank => string.Empty,
                _ => string.Empty
            };
    }

    private static string GetFormulaValue(ICell cell)
    {
        try
        {
            return cell.CachedFormulaResultType switch
            {
                CellType.Numeric => DateUtil.IsCellDateFormatted(cell)
                    ? string.Format("{0:yyyy-MM-dd}", cell.DateCellValue)
                    : cell.NumericCellValue.ToString(),
                CellType.String => cell.StringCellValue,
                CellType.Boolean => cell.BooleanCellValue.ToString(),
                CellType.Blank => string.Empty,
                _ => string.Empty
            };
        }
        catch
        {
            return string.Empty;
        }
    }

    private static bool IsEmptyRow(IRow row) => row.Cells.All(IsBlankCell);

    private static bool IsBlankCell(ICell cell) => cell.CellType == CellType.Blank ||
               cell.CellType == CellType.String && string.IsNullOrWhiteSpace(cell.StringCellValue);
}
