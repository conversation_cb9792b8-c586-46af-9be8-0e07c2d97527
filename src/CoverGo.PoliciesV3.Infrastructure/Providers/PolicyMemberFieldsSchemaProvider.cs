using CoverGo.FeatureManagement;
using CoverGo.Multitenancy;
using CoverGo.PoliciesV3.Application.Common.Interfaces;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.Validators;
using CoverGo.PoliciesV3.Domain.CustomFields.Validation;
using CoverGo.PoliciesV3.Domain.Endorsements;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using Microsoft.Extensions.Logging;

namespace CoverGo.PoliciesV3.Infrastructure.Providers;

/// <summary>
/// Provider implementation for retrieving and processing policy member field schemas
/// Combines Product/Census/Member schemas and can be reused across multiple features
/// </summary>
public class PolicyMemberFieldsSchemaProvider(
    IPolicyMemberFieldsSchemaRepository schemaRepository,
    IMultiTenantFeatureManager featureManager,
    TenantId tenantId,
    ILogger<PolicyMemberFieldsSchemaProvider> logger) : IPolicyMemberFieldsSchemaProvider
{
    private readonly IPolicyMemberFieldsSchemaRepository _schemaRepository = schemaRepository ?? throw new ArgumentNullException(nameof(schemaRepository));
    private readonly IMultiTenantFeatureManager _featureManager = featureManager ?? throw new ArgumentNullException(nameof(featureManager));
    private readonly TenantId _tenantId = tenantId ?? throw new ArgumentNullException(nameof(tenantId));
    private readonly ILogger<PolicyMemberFieldsSchemaProvider> _logger = logger ?? throw new ArgumentNullException(nameof(logger));

    // TODO: Make business people create a user story to implement `Identity` flag/type in member settings UI
    private readonly HashSet<string> _identities = ["passportNo", "hkid", "staffNo"];

    /// <summary>
    /// Gets the base custom fields schema for a specific policy configuration
    /// Used by: Manual member addition, Member management, Member uploads
    /// </summary>
    public async Task<PolicyMemberFieldsSchema> GetCustomFieldsSchema(
        string? contractHolderId,
        ProductId productId,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("GetCustomFieldsSchema - Entry. ContractHolderId: {ContractHolderId}, ProductId: {ProductId}",
            contractHolderId, productId);

        try
        {
            // Get the base schema from repository
            PolicyMemberFieldsSchema schema = await _schemaRepository.GetCustomFieldsSchema(contractHolderId, productId, cancellationToken);

            _logger.LogInformation("GetCustomFieldsSchema - Base schema retrieved. MemberFields: {MemberFieldsCount}, ProductFields: {ProductFieldsCount}, CensusFields: {CensusFieldsCount}",
                schema.MemberFields.Count, schema.ProductFields?.Count ?? 0, schema.CensusFields?.Count ?? 0);

            return schema;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "GetCustomFieldsSchema - Error occurred");
            throw;
        }
    }

    /// <summary>
    /// Gets the schema with upload-specific processing applied
    /// Used by: Member upload features
    /// </summary>
    public async Task<PolicyMemberFieldsSchema> GetMemberUploadSchema(
        string? contractHolderId,
        ProductId productId,
        EndorsementId? endorsementId,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("GetMemberUploadSchema - Entry. ContractHolderId: {ContractHolderId}, ProductId: {ProductId}, EndorsementId: {EndorsementId}",
            contractHolderId, productId, endorsementId);

        // Get base schema
        PolicyMemberFieldsSchema schema = await GetCustomFieldsSchema(contractHolderId, productId, cancellationToken);

        // Apply upload-specific processing
        // Get feature flag
        bool useEffectiveDateInAddPolicyMember = await _featureManager.IsEnabled("UseEffectiveDateInAddPolicyMember", _tenantId.Value);

        schema = AddMissingSystemFieldsToSchema(schema, endorsementId, useEffectiveDateInAddPolicyMember);
        schema = AddIdentityValidations(schema);
        schema = InterceptMemberFieldsToSupportDependentMembers(schema);

        return schema;
    }

    #region Private Helper Methods - Schema Processing

    /// <summary>
    /// Adds missing system fields to the schema based on business rules
    /// </summary>
    private PolicyMemberFieldsSchema AddMissingSystemFieldsToSchema(
        PolicyMemberFieldsSchema schema,
        EndorsementId? endorsementId,
        bool useEffectiveDateInAddPolicyMember)
    {
        _logger.LogInformation("AddMissingSystemFieldsToSchema - Entry. EndorsementId: {EndorsementId}, UseEffectiveDateFlag: {UseEffectiveDateFlag}",
            endorsementId, useEffectiveDateInAddPolicyMember);

        var memberFields = schema.MemberFields.ToList();

        void AddFieldIfMissing(
            string fieldName,
            string label,
            bool isRequired,
            IFieldType fieldType)
        {
            if (!memberFields.Exists(it => it.Name == fieldName))
            {
                memberFields.Add(new PolicyMemberFieldDefinition
                {
                    Name = fieldName,
                    Label = label,
                    IsRequired = isRequired,
                    Type = fieldType,
                    IsUnique = false
                });
                _logger.LogTrace("AddMissingSystemFieldsToSchema - Added missing field: {FieldName}", fieldName);
            }
        }

        AddFieldIfMissing(
            PolicyMemberUploadWellKnowFields.PlanIdField,
            "Plan",
            true,
            new StringFieldType() { Options = null });

        AddFieldIfMissing(
            PolicyMemberUploadWellKnowFields.ClassField,
            "Class",
            false,
            new StringFieldType() { Options = null });

        // Frontend created options: [] in order to change the UI, but it breaks our validation.
        // We already validate planId in PlanIdValidator, so it's easier to just cut it off here.
        PolicyMemberFieldDefinition field = memberFields.Single(it => it.Name == PolicyMemberUploadWellKnowFields.PlanIdField);
        memberFields.Remove(field);
        field = field with
        {
            Type = new StringFieldType() { Options = null }
        };
        memberFields.Add(field);

        if (endorsementId is not null)
        {
            AddFieldIfMissing(
                PolicyMemberUploadWellKnowFields.EffectiveDateField,
                "Effective Date",
                true,
                new DateFieldType());
        }
        else if (useEffectiveDateInAddPolicyMember)
        {
            AddFieldIfMissing(
                PolicyMemberUploadWellKnowFields.EffectiveDateField,
                "Effective Date",
                false, // Not required when added via feature flag
                new DateFieldType());
        }

        AddFieldIfMissing(
            PolicyMemberUploadWellKnowFields.MemberIdField,
            "Member ID",
            false,
            new StringFieldType() { Options = null });

        AddFieldIfMissing(
            PolicyMemberUploadWellKnowFields.DependentOfField,
            "Dependent Of",
            false,
            new StringFieldType() { Options = null });

        if (endorsementId is null && !useEffectiveDateInAddPolicyMember)
        {
            int removedCount = memberFields.RemoveAll(x => x.Name == PolicyMemberUploadWellKnowFields.EffectiveDateField);
            _logger.LogTrace("AddMissingSystemFieldsToSchema - Removed EffectiveDate field: {RemovedCount}", removedCount);
        }

        PolicyMemberFieldsSchema result = schema with
        {
            MemberFields = memberFields,
        };

        return result;
    }

    /// <summary>
    /// Adds identity validations to the schema
    /// </summary>
    private PolicyMemberFieldsSchema AddIdentityValidations(PolicyMemberFieldsSchema schema)
    {
        _logger.LogInformation("AddIdentityValidations - Entry");

        var memberFields = schema.MemberFields.ToList();
        var oneOfValidations = new List<CustomFieldOneOfValidation>();

        var identityValidations = _identities
            .Select(identityFieldName => memberFields.SingleOrDefault(fieldDefinition => fieldDefinition.Name == identityFieldName))
            .Where(field => field is not null)
            .Cast<PolicyMemberFieldDefinition>()
            .Select(field => new CustomFieldRequiredValidation { Field = field, IsRequired = true })
            .ToList();

        if (identityValidations.Count > 0)
        {
            oneOfValidations.Add(new CustomFieldOneOfValidation { Validations = identityValidations });
            _logger.LogInformation("AddIdentityValidations - Added {ValidationCount} identity validations", identityValidations.Count);
        }

        PolicyMemberFieldsSchema result = schema with
        {
            OneOfValidations = oneOfValidations
        };

        return result;
    }

    /// <summary>
    /// Intercepts member fields to support dependent members
    /// </summary>
    private PolicyMemberFieldsSchema InterceptMemberFieldsToSupportDependentMembers(PolicyMemberFieldsSchema schema)
    {
        _logger.LogInformation("InterceptMemberFieldsToSupportDependentMembers - Entry");

        // If someone from the business asks you to add any other fields - we should have UI settings for such fields.
        // Like required for memberType = dependent
        PolicyMemberFieldDefinition? relationshipToDependentField = schema.MemberFields.SingleOrDefault(fieldDefinition => fieldDefinition.Name == "relationshipToEmployee");
        if (relationshipToDependentField == null)
        {
            _logger.LogInformation("InterceptMemberFieldsToSupportDependentMembers - Exit (relationship field not found)");
            return schema;
        }

        var memberFields = schema.MemberFields.ToList();
        memberFields.Remove(relationshipToDependentField);
        relationshipToDependentField = relationshipToDependentField with
        {
            IsRequiredForDependent = true
        };
        memberFields.Add(relationshipToDependentField);
        _logger.LogInformation("InterceptMemberFieldsToSupportDependentMembers - Updated 'relationshipToEmployee' field for dependent requirements");

        PolicyMemberFieldsSchema result = schema with
        {
            MemberFields = memberFields
        };

        return result;
    }
    #endregion
}
