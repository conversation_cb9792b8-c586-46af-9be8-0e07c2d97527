using CoverGo.PoliciesV3.Application.Common.Interfaces;
using CoverGo.PoliciesV3.Application.Services;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;
using CoverGo.PoliciesV3.Infrastructure.Extensions;
using CoverGo.PoliciesV3.Infrastructure.Schemas;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using DomainProductId = CoverGo.PoliciesV3.Domain.Policies.ProductId;
using ClientProductId = CoverGo.Products.Client.ProductId;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.ValidationErrors;

namespace CoverGo.PoliciesV3.Infrastructure.Repositories;

/// <summary>
/// Repository implementation for retrieving raw policy member field schemas from data sources
/// This is focused on data access only - business logic is handled by the provider
/// </summary>
public class PolicyMemberFieldsSchemaRepository(
    IUsersService usersService,
    ICasesService casesService,
    IProductService productService,
    ILogger<PolicyMemberFieldsSchemaRepository> logger) : IPolicyMemberFieldsSchemaRepository
{
    private readonly IUsersService _usersService = usersService ?? throw new ArgumentNullException(nameof(usersService));
    private readonly ICasesService _casesService = casesService ?? throw new ArgumentNullException(nameof(casesService));
    private readonly IProductService _productService = productService ?? throw new ArgumentNullException(nameof(productService));
    private readonly ILogger<PolicyMemberFieldsSchemaRepository> _logger = logger ?? throw new ArgumentNullException(nameof(logger));

    /// <summary>
    /// Gets the raw custom fields schema from data sources (combines Product/Census/Member schemas)
    /// </summary>
    public async Task<PolicyMemberFieldsSchema> GetCustomFieldsSchema(
        string? contractHolderId,
        DomainProductId productId,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Getting custom fields schema for ContractHolderId: {ContractHolderId}, ProductId: {ProductId}",
            contractHolderId, productId);

        cancellationToken.ThrowIfCancellationRequested();

        IReadOnlyList<PolicyMemberFieldDefinition>? censusFields = await GetContractHolderMembersFields(contractHolderId, cancellationToken);
        IReadOnlyList<PolicyMemberFieldDefinition>? productFields = await GetProductMembersFields(productId, cancellationToken);
        IReadOnlyList<PolicyMemberFieldDefinition> memberFields = await GetMembersFields(cancellationToken);

        var schema = new PolicyMemberFieldsSchema
        {
            MemberFields = [.. memberFields],
            ProductFields = productFields?.ToList(),
            CensusFields = censusFields?.ToList(),
            OneOfValidations = null // Will be handled by provider
        };

        _logger.LogInformation("Schema created with {MemberCount} member, {ProductCount} product, {CensusCount} census fields",
            schema.MemberFields.Count, schema.ProductFields?.Count ?? 0, schema.CensusFields?.Count ?? 0);

        return schema;
    }

    #region Private Helper Methods - Service Calls

    /// <summary>
    /// Gets contract holder members fields from UsersService
    /// Maps to: GetCompanyContractHolderMembersFieldsById
    /// </summary>
    private async Task<IReadOnlyList<PolicyMemberFieldDefinition>?> GetContractHolderMembersFields(
        string? contractHolderId,
        CancellationToken cancellationToken)
    {
        if (string.IsNullOrWhiteSpace(contractHolderId))
            return null;

        Newtonsoft.Json.Linq.JToken? fieldsJson = await _usersService.GetCompanyContractHolderMembersFieldsById(contractHolderId, cancellationToken);
        if (fieldsJson == null)
            return null;

        ContractHoldersCensusLevelSchema? companySchema = fieldsJson.ToObject<ContractHoldersCensusLevelSchema>();
        return companySchema?.CensusLevel == null
            ? []
            : [.. companySchema.CensusLevel.Select(field => new PolicyMemberFieldDefinition
        {
            Label = field.Name ?? string.Empty,
            Name = field.Name ?? string.Empty,
            Type = new StringFieldType
            {
                Options = field.Values?.Select(x => new StringOption { Value = x, Label = x }).ToList()
            },
            IsRequired = field.IsRequired,
            IsUnique = false,
        })];
    }

    /// <summary>
    /// Gets product members fields from ProductService
    /// Maps to: GetProductMemberSchema
    /// </summary>
    private async Task<IReadOnlyList<PolicyMemberFieldDefinition>?> GetProductMembersFields(
        DomainProductId productId,
        CancellationToken cancellationToken)
    {
        var clientProductId = new ClientProductId
        {
            Plan = productId.Plan,
            Type = productId.Type,
            Version = productId.Version
        };

        string? schemaJson = await _productService.GetProductMemberSchema(clientProductId, cancellationToken);
        if (string.IsNullOrWhiteSpace(schemaJson))
            return null;

        ProductsDataSchema? pricingScriptDataSchema = JsonConvert.DeserializeObject<ProductsDataSchema>(schemaJson);
        IDictionary<string, PolicyMemberCustomField>? properties = pricingScriptDataSchema?.MemberCustomSchema?.Properties;

        return properties == null ? [] : (IReadOnlyList<PolicyMemberFieldDefinition>)MapCustomFieldsToDefinitions(properties);
    }

    /// <summary>
    /// Gets members fields from CasesService
    /// Maps to: GetMemberDataSchema
    /// </summary>
    private async Task<IReadOnlyList<PolicyMemberFieldDefinition>> GetMembersFields(CancellationToken cancellationToken)
    {
        Newtonsoft.Json.Linq.JToken? schemaJson = await _casesService.GetMemberDataSchema(cancellationToken);
        if (schemaJson == null)
            return [];

        CasesDataSchema? memberSchema = schemaJson.ToObject<CasesDataSchema>();
        return memberSchema?.Properties == null ? [] : (IReadOnlyList<PolicyMemberFieldDefinition>)MapCustomFieldsToDefinitions(memberSchema.Properties, includeObjectFieldParents: true);
    }

    /// <summary>
    /// Maps custom fields dictionary to PolicyMemberFieldDefinition list
    /// </summary>
    private static List<PolicyMemberFieldDefinition> MapCustomFieldsToDefinitions(
        IDictionary<string, PolicyMemberCustomField> properties,
        bool includeObjectFieldParents = false)
    {
        var fieldDefinitions = new List<PolicyMemberFieldDefinition>();

        foreach ((string fieldName, PolicyMemberCustomField fieldType) in properties)
        {
            CustomFieldTypeBase? type = fieldType.ToCustomFieldType();
            if (type == null)
                continue;

            var fieldDefinition = new PolicyMemberFieldDefinition
            {
                Label = fieldType.Meta?.Label ?? fieldName,
                Name = fieldName,
                Type = type,
                IsRequired = fieldType.IsRequired,
                IsUnique = fieldType.IsUnique,
            };

            // Handle nested object fields with parent relationships (only for member fields)
            if (includeObjectFieldParents && fieldDefinition.Type is ObjectFieldType objType)
            {
                fieldDefinition = fieldDefinition with
                {
                    Type = objType with
                    {
                        InnerFieldDefinitions = [.. objType.InnerFieldDefinitions
                            .Select(it => it with { Parent = fieldDefinition })]
                    }
                };
            }

            fieldDefinitions.Add(fieldDefinition);
        }

        return fieldDefinitions;
    }

    #endregion
}