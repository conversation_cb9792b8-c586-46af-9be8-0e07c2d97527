namespace CoverGo.PoliciesV3.Infrastructure.Schemas;

/// <summary>
/// Schema for product data
/// </summary>
public record ProductsDataSchema
{
    public ProductsProperties? MemberCustomSchema { get; init; }
}

/// <summary>
/// Properties container for product schema
/// </summary>
public record ProductsProperties
{
    public string? Description { get; set; }
    public IDictionary<string, PolicyMemberCustomField>? Properties { get; set; }
}

/// <summary>
/// JavaScript type used by FE to represent the field
/// </summary>
public enum PolicyMemberCustomFieldType
{
    String, Number, Boolean, Address, Formula, Files, Object
}

/// <summary>
/// Custom field definition for policy members
/// </summary>
public sealed record PolicyMemberCustomField
{
    public PolicyMemberCustomFieldType Type { get; init; }
    public PolicyMemberCustomFieldMeta? Meta { get; init; }
    public Dictionary<string, PolicyMemberCustomField>? Properties { get; init; }

    public bool IsRequired => Meta?.Required == true;
    public bool IsUnique => CustomFieldValidationRule.HasUnique(Meta?.Validations);
}

/// <summary>
/// Metadata for custom fields
/// </summary>
public class PolicyMemberCustomFieldMeta
{
    /// <summary>
    /// How it's displayed in UI
    /// </summary>
    public required string Label { get; init; }

    /// <summary>
    /// Field type set by the User of an application
    /// </summary>
    public string? FieldType { get; init; }

    /// <summary>
    /// Which component FE uses
    /// </summary>
    public string? Component { get; init; }

    /// <summary>
    /// Whether the field is required
    /// </summary>
    public bool? Required { get; init; }

    /// <summary>
    /// Order of display in UI
    /// </summary>
    public double? Order { get; init; }

    /// <summary>
    /// FE Validation
    /// </summary>
    public string? Validations { get; init; }

    /// <summary>
    /// Optional list of possible options
    /// </summary>
    public List<PolicyMemberCustomFieldOption>? Options { get; init; }

    /// <summary>
    /// Formula definitions
    /// </summary>
    public List<PolicyMemberCustomFieldFormula?>? Formula { get; init; }

    /// <summary>
    /// Conditional logic
    /// </summary>
    public string? Condition { get; init; }
}

/// <summary>
/// Option for custom fields
/// </summary>
public class PolicyMemberCustomFieldOption
{
    /// <summary>
    /// Some random number
    /// </summary>
    public string? Key { get; init; }

    /// <summary>
    /// How it's displayed in UI
    /// </summary>
    public string? Name { get; init; }

    /// <summary>
    /// How FE passed the field to BE
    /// </summary>
    public required object Value { get; init; }
}

/// <summary>
/// Validation rule utilities
/// </summary>
public static class CustomFieldValidationRule
{
    public const string Unique = "unique";

    public static bool HasUnique(string? validations) => validations?.Contains(Unique, StringComparison.OrdinalIgnoreCase) == true;
}
