using System.Collections.Immutable;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.ValidationErrors;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes.Values;
using CoverGo.PoliciesV3.Infrastructure.Schemas;

namespace CoverGo.PoliciesV3.Infrastructure.Extensions;

/// <summary>
/// Extension methods for converting schema types to domain field types
/// </summary>
public static class CustomFieldExtensions
{
    /// <summary>
    /// Converts PolicyMemberCustomField to CustomFieldTypeBase
    /// </summary>
    public static CustomFieldTypeBase? ToCustomFieldType(this PolicyMemberCustomField field)
    {
        CustomFieldTypeBase type = field.Type switch
        {
            PolicyMemberCustomFieldType.String => field.Meta?.FieldType switch
            {
                "date" => new DateFieldType(),
                "dynamic" => CreateFormulaFieldType(field.Meta),
                _ => new StringFieldType
                {
                    Options = field.Meta?.Options?.Select(x => new StringOption 
                    { 
                        Value = x.Value.ToString() ?? string.Empty, 
                        Label = x.Name 
                    }).ToList(),
                    Validations = field.Meta?.Validations
                }
            },
            PolicyMemberCustomFieldType.Boolean => new BooleanFieldType(),
            PolicyMemberCustomFieldType.Number => new NumberFieldType
            {
                Options = field.Meta?.Options?.Select(x => new NumberOption
                {
                    Value = NumberValue.Create(x.Value),
                    Label = x.Name ?? string.Empty
                }).ToList()
            },
            PolicyMemberCustomFieldType.Address => CreateAddressFieldType(field),
            PolicyMemberCustomFieldType.Files => new FilesFieldType(),
            PolicyMemberCustomFieldType.Formula => CreateFormulaFieldType(field.Meta),
            PolicyMemberCustomFieldType.Object => new ObjectFieldType(Enumerable.Empty<PolicyMemberFieldDefinition>().ToImmutableList()),
            _ => throw new InvalidOperationException("Failed to get field type from the schema")
        };

        return type;
    }

    private static FormulaFieldType CreateFormulaFieldType(PolicyMemberCustomFieldMeta? meta)
    {
        if (meta?.Formula == null || meta.Formula.Count == 0)
        {
            return new FormulaFieldType { Func = null };
        }

        PolicyMemberCustomFieldFormula? formula = meta.Formula[0];
        if (formula?.Children == null || formula.Children.Count == 0)
        {
            return new FormulaFieldType { Func = null };
        }

        var inputs = formula.Children
            .Take(formula.Children.Count - 1)
            .Where(child => child != null)
            .Select(child => CreateFormulaParameter(child!))
            .ToList();

        PolicyMemberCustomFieldFormulaChild? separator = formula.Children.LastOrDefault();
        IFormulaParameter separatorParam = separator != null ? CreateFormulaParameter(separator) : new FormulaValue { Value = "" };

        return new FormulaFieldType
        {
            Func = new JoinFormula
            {
                Inputs = inputs,
                Separator = separatorParam
            }
        };
    }

    private static IFormulaParameter CreateFormulaParameter(PolicyMemberCustomFieldFormulaChild child) => child switch
    {
        PolicyMemberCustomFieldFormulaValueChild valueChild => new FormulaValue { Value = valueChild.Props.Value },
        PolicyMemberCustomFieldFormulaDataChild dataChild => new FormulaData { Path = dataChild.Props.Path },
        _ => new FormulaValue { Value = null }
    };

    private static AddressFieldType CreateAddressFieldType(PolicyMemberCustomField field)
    {
        if (field.Properties == null)
            throw new InvalidOperationException("Address type should have defined properties");

        var fieldDefinitions = new List<PolicyMemberFieldDefinition>();
        foreach ((string propertyName, PolicyMemberCustomField property) in field.Properties)
        {
            CustomFieldTypeBase? fieldType = property.ToCustomFieldType();
            if (fieldType == null) continue;

            var fieldDefinition = new PolicyMemberFieldDefinition
            {
                Name = propertyName,
                Label = property.Meta?.Label ?? propertyName,
                Type = fieldType,
                IsRequired = property.IsRequired,
                IsUnique = property.IsUnique
            };

            fieldDefinitions.Add(fieldDefinition);
        }

        return new AddressFieldType(fieldDefinitions);
    }
}
