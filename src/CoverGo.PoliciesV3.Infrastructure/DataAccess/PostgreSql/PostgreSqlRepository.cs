using CoverGo.BuildingBlocks.Domain.Core.Entities;
using CoverGo.PoliciesV3.Application.Common;
using Microsoft.EntityFrameworkCore;
using System.Data;
using System.Linq.Expressions;

namespace CoverGo.PoliciesV3.Infrastructure.DataAccess.PostgreSql;

internal class PostgreSqlRepository<TDbContext, TAggregateRoot, TIdentity>(PostgreSqlUnitOfWork<TDbContext> unitOfWork)
    : IPaginatedRepository<TAggregateRoot, TIdentity>
    where TDbContext : DbContext
    where TAggregateRoot : class, IAggregateRoot<TIdentity>
{
    private readonly DbSet<TAggregateRoot> _entity = unitOfWork.DbContext.Set<TAggregateRoot>();

    public async Task<TAggregateRoot> GetByIdAsync(TIdentity id, CancellationToken cancellationToken) => await _entity.FindAsync([id], cancellationToken) ?? throw new InvalidOperationException($"Entity with {nameof(id)} {id} not found.");

    public async Task<TAggregateRoot> FindByIdAsync(TIdentity id, CancellationToken cancellationToken)
    {
        TAggregateRoot? entity = await _entity.FindAsync([id], cancellationToken);
        return entity!;
    }

    public async Task<TAggregateRoot> SingleOrDefaultAsync(CancellationToken cancellationToken)
    {
        TAggregateRoot? entity = await _entity.SingleOrDefaultAsync(cancellationToken);
        return entity!;
    }

    public async Task<TAggregateRoot> InsertAsync(TAggregateRoot entity, CancellationToken cancellationToken)
    {
        await _entity.AddAsync(entity, cancellationToken);
        if (!unitOfWork.HasActiveTransaction)
            await unitOfWork.DbContext.SaveChangesAsync(cancellationToken);
        return entity;
    }

    public async Task<TAggregateRoot> UpdateAsync(TAggregateRoot entity, CancellationToken cancellationToken)
    {
        _entity.Update(entity);
        if (!unitOfWork.HasActiveTransaction)
            await unitOfWork.DbContext.SaveChangesAsync(cancellationToken);
        return entity;
    }

    public async Task DeleteAsync(TIdentity id, CancellationToken cancellationToken)
    {
        TAggregateRoot entity = await GetByIdAsync(id, cancellationToken);
        _entity.Remove(entity);
        if (!unitOfWork.HasActiveTransaction)
            await unitOfWork.DbContext.SaveChangesAsync(cancellationToken);
    }

    public async Task<List<TAggregateRoot>> FindAllAsync(List<TIdentity> ids, CancellationToken cancellationToken) => await _entity.Where(e => ids.Contains(EF.Property<TIdentity>(e, nameof(IAggregateRoot<TIdentity>.Id)))).ToListAsync(cancellationToken);

    public async Task<IEnumerable<TAggregateRoot>> FindAllByAsync(Expression<Func<TAggregateRoot, bool>> filterExpression, CancellationToken cancellationToken)
    {
        IQueryable<TAggregateRoot> query = _entity.Where(filterExpression);
        return await query.ToListAsync(cancellationToken);
    }

    public async Task UpdateBatchAsync(List<TAggregateRoot> aggregates, CancellationToken cancellationToken)
    {
        _entity.UpdateRange(aggregates);
        if (!unitOfWork.HasActiveTransaction)
            await unitOfWork.DbContext.SaveChangesAsync(cancellationToken);
    }

    public async Task InsertBatchAsync(List<TAggregateRoot> aggregates, CancellationToken cancellationToken)
    {
        await _entity.AddRangeAsync(aggregates, cancellationToken);
        if (!unitOfWork.HasActiveTransaction)
            await unitOfWork.DbContext.SaveChangesAsync(cancellationToken);
    }

    public async Task DeleteBatchAsync(List<TIdentity> ids, CancellationToken cancellationToken)
    {
        List<TAggregateRoot> entities = await FindAllAsync(ids, cancellationToken);
        _entity.RemoveRange(entities);
        if (!unitOfWork.HasActiveTransaction)
            await unitOfWork.DbContext.SaveChangesAsync(cancellationToken);
    }

    public async Task DeleteBatchAsync(List<TAggregateRoot> aggregates, CancellationToken cancellationToken)
    {
        _entity.RemoveRange(aggregates);
        if (!unitOfWork.HasActiveTransaction)
            await unitOfWork.DbContext.SaveChangesAsync(cancellationToken);
    }

    public async Task<PageResult<TAggregateRoot>> GetPagedAsync(
        Expression<Func<TAggregateRoot, bool>> filterExpression,
        int skip,
        int take,
        string orderBy = nameof(IAggregateRoot<TIdentity>.Id),
        string orderDirection = "ASC",
        CancellationToken cancellationToken = default)
    {
        // Start with the filtered query
        IQueryable<TAggregateRoot> query = _entity.Where(filterExpression);

        // Count total records that match the filter
        int totalCount = await query.CountAsync(cancellationToken);

        // Apply ordering based on the property name
        query = ApplyOrderBy(query, orderBy, orderDirection);

        // Apply pagination
        query = query.Skip(skip).Take(take);

        // Execute query and get results
        List<TAggregateRoot> items = await query.ToListAsync(cancellationToken);

        // Return the paginated result
        return new PageResult<TAggregateRoot>(items, totalCount, skip / take + 1, take);
    }

    private static IQueryable<TAggregateRoot> ApplyOrderBy(IQueryable<TAggregateRoot> query, string orderBy, string orderDirection)
    {
        // Get property info for the specified property name
        Type entityType = typeof(TAggregateRoot);
        System.Reflection.PropertyInfo propertyInfo = entityType.GetProperty(orderBy) ??
                           entityType.GetProperties().FirstOrDefault() ??
                           throw new InvalidOperationException($"Property {orderBy} not found on type {entityType.Name}");

        // Create a parameter expression for the lambda
        ParameterExpression parameter = Expression.Parameter(entityType, "x");

        // Create property access expression
        MemberExpression property = Expression.Property(parameter, propertyInfo);

        // Create lambda expression
        LambdaExpression lambda = Expression.Lambda(property, parameter);

        // Determine if we need to sort ascending or descending
        string methodName = orderDirection.Equals("DESC", StringComparison.OrdinalIgnoreCase)
            ? "OrderByDescending"
            : "OrderBy";

        // Get the generic method info for OrderBy/OrderByDescending
        System.Reflection.MethodInfo orderByMethod = typeof(Queryable)
            .GetMethods()
            .First(m => m.Name == methodName && m.GetParameters().Length == 2)
            .MakeGenericMethod(entityType, propertyInfo.PropertyType);

        // Invoke the method on the query
        return (IQueryable<TAggregateRoot>)orderByMethod.Invoke(null, [query, lambda])!;
    }
}