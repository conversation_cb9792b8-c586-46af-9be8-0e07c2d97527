using CoverGo.PoliciesV3.Domain.Endorsements;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

namespace CoverGo.PoliciesV3.Infrastructure.DataAccess.Configurations.Endorsements;

public abstract class EndorsementConverter
{
    public class IdConverter() : ValueConverter<EndorsementId, Guid>(id => id.Value, value => new EndorsementId(value));
    public class NullableIdConverter() : ValueConverter<EndorsementId?, Guid?>(x => x != null ? x.Value : null, x => x.HasValue ? new EndorsementId(x.Value) : null);
}
