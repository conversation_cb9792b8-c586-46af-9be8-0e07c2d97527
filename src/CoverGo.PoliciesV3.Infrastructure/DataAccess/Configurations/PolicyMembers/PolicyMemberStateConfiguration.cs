using CoverGo.PoliciesV3.Domain.PolicyMembers;
using CoverGo.PoliciesV3.Domain.ValueObjects;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Infrastructure.Common.Constants;
using CoverGo.PoliciesV3.Infrastructure.Common.Extensions;
using CoverGo.PoliciesV3.Infrastructure.Common.Helpers;
using CoverGo.PoliciesV3.Infrastructure.DataAccess.PostgreSql;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using System.Text.Json;

namespace CoverGo.PoliciesV3.Infrastructure.DataAccess.Configurations.PolicyMembers;

public class PolicyMemberStateConfiguration : IEntityTypeConfiguration<PolicyMemberState>
{
    public void Configure(EntityTypeBuilder<PolicyMemberState> builder)
    {
        // Table mapping
        builder.ToTable(PostgreSqlHelpers.GetTableName(nameof(PolicyMemberState)));
        builder.HasPrimaryKeyWithAutoName(x => x.Id);

        #region Primary and Foreign Keys

        builder.Property(x => x.Id)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberState.Id)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Uuid)
            .ValueGeneratedOnAdd();

        builder.Property(x => x.PolicyMemberId)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberState.PolicyMemberId)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Uuid)
            .IsRequired();

        builder.Property(x => x.EndorsementId)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberState.EndorsementId)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Uuid)
            .IsRequired(false);

        #endregion

        #region Required Properties

        builder.Property(x => x.StartDate)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberState.StartDate)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Date)
            .IsRequired();

        builder.Property(x => x.EndDate)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberState.EndDate)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Date)
            .IsRequired();

        builder.Property(x => x.PlanId)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberState.PlanId)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Varchar255)
            .IsRequired();

        #endregion

        #region Optional Properties

        builder.Property(x => x.Class)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberState.Class)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Varchar255)
            .IsRequired(false);

        builder.Property(x => x.HealthQuestionnaireResponseId)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberState.HealthQuestionnaireResponseId)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Varchar255)
            .IsRequired(false);

        #endregion

        #region Enum Properties

        builder.Property(x => x.ValidationResult)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberState.ValidationResult)))
            .IsRequired(false);

        builder.Property(x => x.UnderwritingResult)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberState.UnderwritingResult)))
            .IsRequired(false);

        #endregion

        #region JSONB Properties

        builder.Property(x => x.Fields)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberState.Fields)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Jsonb)
            .HasConversion(
                v => JsonSerializer.Serialize(v, JsonSerializationHelpers.DatabaseJsonOptions),
                v => JsonSerializer.Deserialize<List<PolicyField>>(v, JsonSerializationHelpers.DatabaseJsonOptions) ?? new List<PolicyField>())
            .IsRequired();

        builder.Property(x => x.Premium)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberState.Premium)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Jsonb)
            .HasConversion(
                v => v == null ? null : JsonSerializer.Serialize(v, JsonSerializationHelpers.DatabaseJsonOptions),
                v => v == null ? null : JsonSerializer.Deserialize<MemberPremium>(v, JsonSerializationHelpers.DatabaseJsonOptions))
            .IsRequired(false);

        builder.Property(x => x.Underwriting)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberState.Underwriting)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Jsonb)
            .HasConversion(
                v => v == null ? null : JsonSerializer.Serialize(v, JsonSerializationHelpers.DatabaseJsonOptions),
                v => v == null ? null : JsonSerializer.Deserialize<MemberUnderwriting>(v, JsonSerializationHelpers.DatabaseJsonOptions))
            .IsRequired(false);

        builder.Property(x => x.Loadings)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberState.Loadings)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Jsonb)
            .HasConversion(
                v => v == null ? null : JsonSerializer.Serialize(v, JsonSerializationHelpers.DatabaseJsonOptions),
                v => v == null ? null : JsonSerializer.Deserialize<List<MemberLoading>>(v, JsonSerializationHelpers.DatabaseJsonOptions) as ICollection<MemberLoading>)
            .IsRequired(false);

        builder.Property(x => x.BenefitsLoadings)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberState.BenefitsLoadings)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Jsonb)
            .HasConversion(
                v => v == null ? null : JsonSerializer.Serialize(v, JsonSerializationHelpers.DatabaseJsonOptions),
                v => v == null ? null : JsonSerializer.Deserialize<List<MemberBenefitsLoading>>(v, JsonSerializationHelpers.DatabaseJsonOptions) as ICollection<MemberBenefitsLoading>)
            .IsRequired(false);

        builder.Property(x => x.BenefitsUnderwritings)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberState.BenefitsUnderwritings)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Jsonb)
            .HasConversion(
                v => v == null ? null : JsonSerializer.Serialize(v, JsonSerializationHelpers.DatabaseJsonOptions),
                v => v == null ? null : JsonSerializer.Deserialize<List<MemberBenefitsUnderwriting>>(v, JsonSerializationHelpers.DatabaseJsonOptions) as ICollection<MemberBenefitsUnderwriting>)
            .IsRequired(false);

        #endregion

        #region Concurrency Control

        builder.Property(e => e.RowVersion)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberState.RowVersion)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Xid)
            .ValueGeneratedOnAddOrUpdate()
            .IsConcurrencyToken();

        #endregion

        #region Audit Information

        builder.OwnsOne(p => p.EntityAuditInfo, audit =>
        {
            audit.Property(a => a.CreatedAt)
                .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(CoverGo.BuildingBlocks.Domain.Core.Audit.EntityAuditInfo.CreatedAt)))
                .IsRequired();

            audit.Property(a => a.CreatedBy)
                .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(CoverGo.BuildingBlocks.Domain.Core.Audit.EntityAuditInfo.CreatedBy)))
                .HasMaxLength(100)
                .IsRequired();

            audit.Property(a => a.LastModifiedAt)
                .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(CoverGo.BuildingBlocks.Domain.Core.Audit.EntityAuditInfo.LastModifiedAt)))
                .IsRequired(false);

            audit.Property(a => a.LastModifiedBy)
                .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(CoverGo.BuildingBlocks.Domain.Core.Audit.EntityAuditInfo.LastModifiedBy)))
                .HasMaxLength(100)
                .IsRequired(false);

            audit.Property(a => a.DeletedAt)
                .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(CoverGo.BuildingBlocks.Domain.Core.Audit.EntityAuditInfo.DeletedAt)))
                .IsRequired(false);

            audit.Property(a => a.DeletedBy)
                .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(CoverGo.BuildingBlocks.Domain.Core.Audit.EntityAuditInfo.DeletedBy)))
                .IsRequired(false);
        });

        #endregion

        #region Indexes

        builder.HasIndexWithAutoName(x => x.PolicyMemberId);

        builder.HasCompositeIndexWithAutoName(
            nameof(PolicyMemberState.PolicyMemberId),
            nameof(PolicyMemberState.StartDate),
            nameof(PolicyMemberState.EndDate));

        #endregion

        #region Relationships

        builder.HasOne(x => x.PolicyMember)
            .WithMany(x => x.States)
            .HasForeignKey(x => x.PolicyMemberId)
            .HasConstraintName(DatabaseNamingHelpers.GetForeignKeyName<PolicyMemberState, PolicyMember>(nameof(PolicyMemberState.PolicyMemberId)))
            .OnDelete(DeleteBehavior.Restrict);

        #endregion
    }
}