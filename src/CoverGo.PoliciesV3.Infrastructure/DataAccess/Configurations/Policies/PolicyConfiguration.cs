using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMembers;
using CoverGo.PoliciesV3.Infrastructure.Common.Constants;
using CoverGo.PoliciesV3.Infrastructure.Common.Extensions;
using CoverGo.PoliciesV3.Infrastructure.Common.Helpers;
using CoverGo.PoliciesV3.Infrastructure.DataAccess.PostgreSql;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using System.Text.Json;

namespace CoverGo.PoliciesV3.Infrastructure.DataAccess.Configurations.Policies;

public class PolicyConfiguration : IEntityTypeConfiguration<Policy>
{
    public void Configure(EntityTypeBuilder<Policy> builder)
    {
        // Table mapping
        builder.ToTable(PostgreSqlHelpers.GetTableName(nameof(Policy)));
        builder.HasPrimaryKeyWithAutoName(x => x.Id);

        #region Primary and Foreign Keys

        builder.Property(x => x.Id)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(Policy.Id)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Uuid)
            .ValueGeneratedOnAdd();

        builder.Property(x => x.ContractHolderId)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(Policy.ContractHolderId)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Uuid)
            .IsRequired(false);

        #endregion

        #region Required Properties

        builder.Property(x => x.OriginalPolicyNumber)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(Policy.OriginalPolicyNumber)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Varchar255)
            .IsRequired();

        #endregion

        #region Date Properties

        builder.Property(x => x.StartDate)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(Policy.StartDate)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Date)
            .IsRequired(false);

        builder.Property(x => x.EndDate)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(Policy.EndDate)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Date)
            .IsRequired(false);

        builder.Property(x => x.IssueDate)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(Policy.IssueDate)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Timestamp)
            .IsRequired(false);

        #endregion

        #region Boolean Properties

        builder.Property(x => x.IsIssued)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(Policy.IsIssued)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Boolean)
            .HasDefaultValue(false)
            .IsRequired();

        builder.Property(x => x.IsPremiumOverriden)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(Policy.IsPremiumOverriden)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Boolean)
            .HasDefaultValue(false)
            .IsRequired();

        builder.Property(x => x.IsRenewal)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(Policy.IsRenewal)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Boolean)
            .HasDefaultValue(false)
            .IsRequired();

        #endregion

        #region Enum Properties

        builder.Property(x => x.Status)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(Policy.Status)))
            .IsRequired();

        #endregion

        #region Optional Properties

        builder.Property(x => x.CancellationReason)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(Policy.CancellationReason)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Varchar255)
            .IsRequired(false);

        #endregion

        #region JSONB Properties

        builder.Property(x => x.ProductId)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(Policy.ProductId)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Jsonb)
            .HasConversion(
                v => v == null ? null : JsonSerializer.Serialize(v, JsonSerializationHelpers.DatabaseJsonOptions),
                v => v == null ? null : JsonSerializer.Deserialize<ProductId>(v, JsonSerializationHelpers.DatabaseJsonOptions))
            .IsRequired(false);

        builder.Property(x => x.Fields)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(Policy.Fields)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Jsonb)
            .HasConversion(
                v => JsonSerializer.Serialize(v, JsonSerializationHelpers.DatabaseJsonOptions),
                v => JsonSerializer.Deserialize<List<PolicyField>>(v, JsonSerializationHelpers.DatabaseJsonOptions) ?? new List<PolicyField>())
            .IsRequired();

        #endregion

        #region Audit Configuration

        builder.OwnsOne(p => p.EntityAuditInfo, audit =>
        {
            audit.Property(a => a.CreatedAt)
                .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(CoverGo.BuildingBlocks.Domain.Core.Audit.EntityAuditInfo.CreatedAt)))
                .IsRequired();

            audit.Property(a => a.CreatedBy)
                .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(CoverGo.BuildingBlocks.Domain.Core.Audit.EntityAuditInfo.CreatedBy)))
                .HasMaxLength(100)
                .IsRequired();

            audit.Property(a => a.LastModifiedAt)
                .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(CoverGo.BuildingBlocks.Domain.Core.Audit.EntityAuditInfo.LastModifiedAt)))
                .IsRequired(false);

            audit.Property(a => a.LastModifiedBy)
                .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(CoverGo.BuildingBlocks.Domain.Core.Audit.EntityAuditInfo.LastModifiedBy)))
                .HasMaxLength(100)
                .IsRequired(false);

            audit.Property(a => a.DeletedAt)
                .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(CoverGo.BuildingBlocks.Domain.Core.Audit.EntityAuditInfo.DeletedAt)))
                .IsRequired(false);

            audit.Property(a => a.DeletedBy)
                .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(CoverGo.BuildingBlocks.Domain.Core.Audit.EntityAuditInfo.DeletedBy)))
                .IsRequired(false);
        });

        #endregion

        #region Indexes

        builder.HasUniqueIndexWithAutoName(x => x.OriginalPolicyNumber);

        builder.HasIndexWithAutoName(x => x.Status);

        #endregion

        #region Relationships

        builder.HasMany(x => x.PolicyMembers)
            .WithOne(x => x.Policy)
            .HasForeignKey(x => x.PolicyId)
            .HasConstraintName(DatabaseNamingHelpers.GetForeignKeyName<PolicyMember, Policy>(nameof(PolicyMember.PolicyId)))
            .OnDelete(DeleteBehavior.Cascade);

        #endregion
    }
}