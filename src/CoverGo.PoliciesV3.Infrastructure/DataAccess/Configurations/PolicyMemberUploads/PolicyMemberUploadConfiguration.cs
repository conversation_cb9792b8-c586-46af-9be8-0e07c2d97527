using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Infrastructure.Common.Constants;
using CoverGo.PoliciesV3.Infrastructure.Common.Extensions;
using CoverGo.PoliciesV3.Infrastructure.DataAccess.PostgreSql;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace CoverGo.PoliciesV3.Infrastructure.DataAccess.Configurations.PolicyMemberUploads;

public class PolicyMemberUploadConfiguration : IEntityTypeConfiguration<PolicyMemberUpload>
{
    public void Configure(EntityTypeBuilder<PolicyMemberUpload> builder)
    {
        // Table mapping
        builder.ToTable(PostgreSqlHelpers.GetTableName(nameof(PolicyMemberUpload)));
        builder.HasPrimaryKeyWithAutoName(x => x.Id);

        #region Primary and Foreign Keys

        builder.Property(x => x.Id)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberUpload.Id)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Uuid)
            .ValueGeneratedOnAdd();

        builder.Property(x => x.PolicyId)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberUpload.PolicyId)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Uuid)
            .IsRequired();

        builder.Property(x => x.EndorsementId)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberUpload.EndorsementId)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Uuid)
            .IsRequired(false);

        #endregion

        #region Required Properties

        builder.Property(x => x.Path)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberUpload.Path)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Text)
            .IsRequired();

        builder.Property(x => x.MembersCount)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberUpload.MembersCount)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Integer)
            .IsRequired();

        #endregion

        #region Optional Properties

        builder.Property(x => x.ValidMembersCount)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberUpload.ValidMembersCount)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Integer)
            .IsRequired(false);

        builder.Property(x => x.InvalidMembersCount)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberUpload.InvalidMembersCount)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Integer)
            .IsRequired(false);

        #endregion

        #region Status Property

        builder.Property(x => x.Status)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberUpload.Status)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Varchar50)
            .HasDefaultValue(PolicyMemberUploadStatus.REGISTERED)
            .IsRequired();

        #endregion

        #region Timestamp Properties

        builder.Property(x => x.CreatedAt)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberUpload.CreatedAt)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.TimestampWithTimeZone)
            .ValueGeneratedOnAdd()
            .IsRequired();

        builder.Property(x => x.LastModifiedAt)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberUpload.LastModifiedAt)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.TimestampWithTimeZone)
            .IsRequired(false);

        #endregion

        #region Audit Configuration

        builder.OwnsOne(p => p.EntityAuditInfo, audit =>
        {
            audit.Property(a => a.CreatedAt)
                .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(CoverGo.BuildingBlocks.Domain.Core.Audit.EntityAuditInfo.CreatedAt)))
                .IsRequired();

            audit.Property(a => a.CreatedBy)
                .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(CoverGo.BuildingBlocks.Domain.Core.Audit.EntityAuditInfo.CreatedBy)))
                .HasMaxLength(100)
                .IsRequired();

            audit.Property(a => a.LastModifiedAt)
                .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(CoverGo.BuildingBlocks.Domain.Core.Audit.EntityAuditInfo.LastModifiedAt)))
                .IsRequired(false);

            audit.Property(a => a.LastModifiedBy)
                .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(CoverGo.BuildingBlocks.Domain.Core.Audit.EntityAuditInfo.LastModifiedBy)))
                .HasMaxLength(100)
                .IsRequired(false);

            audit.Property(a => a.DeletedAt)
                .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(CoverGo.BuildingBlocks.Domain.Core.Audit.EntityAuditInfo.DeletedAt)))
                .IsRequired(false);

            audit.Property(a => a.DeletedBy)
                .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(CoverGo.BuildingBlocks.Domain.Core.Audit.EntityAuditInfo.DeletedBy)))
                .IsRequired(false);
        });

        #endregion

        #region Indexes

        builder.HasIndexWithAutoName(x => x.PolicyId);

        builder.HasIndexWithAutoName(x => x.EndorsementId);

        builder.HasIndexWithAutoName(x => x.Status);

        builder.HasCompositeIndexWithAutoName(
            nameof(PolicyMemberUpload.PolicyId),
            nameof(PolicyMemberUpload.Status));

        #endregion
    }
}