using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Infrastructure.Common.Constants;
using CoverGo.PoliciesV3.Infrastructure.Common.Extensions;
using CoverGo.PoliciesV3.Infrastructure.Common.Helpers;
using CoverGo.PoliciesV3.Infrastructure.DataAccess.PostgreSql;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace CoverGo.PoliciesV3.Infrastructure.DataAccess.Configurations.PolicyMemberUploads;

public class PolicyMemberUploadImportedResultConfiguration : IEntityTypeConfiguration<PolicyMemberUploadImportedResult>
{
    public void Configure(EntityTypeBuilder<PolicyMemberUploadImportedResult> builder)
    {
        // Table mapping
        builder.ToTable(PostgreSqlHelpers.GetTableName(nameof(PolicyMemberUploadImportedResult)));
        builder.HasPrimaryKeyWithAutoName(x => x.Id);

        #region Primary and Foreign Keys

        builder.Property(x => x.Id)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberUploadImportedResult.Id)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Uuid)
            .ValueGeneratedOnAdd();

        builder.Property(x => x.PolicyMemberUploadId)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberUploadImportedResult.PolicyMemberUploadId)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Uuid)
            .IsRequired();

        builder.Property(x => x.PolicyMemberId)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberUploadImportedResult.PolicyMemberId)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Uuid)
            .IsRequired(false);

        #endregion

        #region Required Properties

        builder.Property(x => x.RowIndex)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberUploadImportedResult.RowIndex)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Integer)
            .IsRequired();

        builder.Property(x => x.Success)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberUploadImportedResult.Success)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Boolean)
            .IsRequired();

        #endregion

        #region Optional Properties

        builder.Property(x => x.ImportingErrorJson)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberUploadImportedResult.ImportingErrorJson)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Jsonb)
            .IsRequired(false);

        #endregion

        #region Indexes

        builder.HasIndexWithAutoName(x => x.PolicyMemberUploadId);

        builder.HasIndexWithAutoName(x => x.RowIndex);

        builder.HasCompositeIndexWithAutoName(
            nameof(PolicyMemberUploadImportedResult.PolicyMemberUploadId),
            nameof(PolicyMemberUploadImportedResult.RowIndex))
            .IsUnique();

        #endregion

        #region Relationships

        builder.HasOne(x => x.PolicyMemberUpload)
            .WithMany(x => x.ImportedResults)
            .HasForeignKey(x => x.PolicyMemberUploadId)
            .HasConstraintName(DatabaseNamingHelpers.GetForeignKeyName<PolicyMemberUploadImportedResult, PolicyMemberUpload>(nameof(PolicyMemberUploadImportedResult.PolicyMemberUploadId)))
            .OnDelete(DeleteBehavior.Cascade);

        #endregion
    }
}