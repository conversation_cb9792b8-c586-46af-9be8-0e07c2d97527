using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Infrastructure.Common.Constants;
using CoverGo.PoliciesV3.Infrastructure.Common.Extensions;
using CoverGo.PoliciesV3.Infrastructure.Common.Helpers;
using CoverGo.PoliciesV3.Infrastructure.DataAccess.PostgreSql;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace CoverGo.PoliciesV3.Infrastructure.DataAccess.Configurations.PolicyMemberUploads;

public class PolicyMemberUploadValidationErrorConfiguration : IEntityTypeConfiguration<PolicyMemberUploadValidationError>
{
    public void Configure(EntityTypeBuilder<PolicyMemberUploadValidationError> builder)
    {
        // Table mapping
        builder.ToTable(PostgreSqlHelpers.GetTableName(nameof(PolicyMemberUploadValidationError)));
        builder.HasPrimaryKeyWithAutoName(x => x.Id);

        #region Primary and Foreign Keys

        builder.Property(x => x.Id)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberUploadValidationError.Id)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Uuid)
            .ValueGeneratedOnAdd();

        builder.Property(x => x.PolicyMemberUploadId)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberUploadValidationError.PolicyMemberUploadId)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Uuid)
            .IsRequired();

        #endregion

        #region Required Properties

        builder.Property(x => x.RowIndex)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberUploadValidationError.RowIndex)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Integer)
            .IsRequired();

        builder.Property(x => x.Code)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberUploadValidationError.Code)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Varchar100)
            .IsRequired();

        builder.Property(x => x.Message)
            .HasColumnName(PostgreSqlHelpers.GetColumnName(nameof(PolicyMemberUploadValidationError.Message)))
            .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Text)
            .IsRequired();

        #endregion

        #region Indexes

        builder.HasIndexWithAutoName(x => x.PolicyMemberUploadId);

        builder.HasIndexWithAutoName(x => x.RowIndex);

        builder.HasIndexWithAutoName(x => x.Code);

        builder.HasCompositeIndexWithAutoName(
            nameof(PolicyMemberUploadValidationError.PolicyMemberUploadId),
            nameof(PolicyMemberUploadValidationError.RowIndex));

        #endregion

        #region Relationships

        builder.HasOne(x => x.PolicyMemberUpload)
            .WithMany(x => x.ValidationErrors)
            .HasForeignKey(x => x.PolicyMemberUploadId)
            .HasConstraintName(DatabaseNamingHelpers.GetForeignKeyName<PolicyMemberUploadValidationError, PolicyMemberUpload>(nameof(PolicyMemberUploadValidationError.PolicyMemberUploadId)))
            .OnDelete(DeleteBehavior.Cascade);

        #endregion
    }
}