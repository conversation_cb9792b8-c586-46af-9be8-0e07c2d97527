using Microsoft.EntityFrameworkCore;

namespace CoverGo.PoliciesV3.Infrastructure.DataAccess.Extensions;

/// <summary>
/// Simple extensions for query optimization
/// </summary>
public static class QueryExtensions
{
    /// <summary>
    /// Forces EF Core to use a single query instead of split queries when data consistency is critical.
    ///
    /// <para><strong>Performance Trade-offs:</strong></para>
    /// <list type="bullet">
    /// <item><description><strong>Split Queries (Default):</strong> Better performance, lower memory usage, but potential for inconsistent data if database changes between queries</description></item>
    /// <item><description><strong>Single Query (This Method):</strong> Guaranteed consistency within a single transaction, but higher memory usage and potential for Cartesian explosion with multiple includes</description></item>
    /// </list>
    ///
    /// <para><strong>When to Use:</strong></para>
    /// <list type="bullet">
    /// <item><description>Financial calculations requiring exact consistency</description></item>
    /// <item><description>Audit trails where data integrity is critical</description></item>
    /// <item><description>Reports where all related data must reflect the same point in time</description></item>
    /// <item><description>Small result sets with multiple includes where Cartesian explosion is manageable</description></item>
    /// </list>
    ///
    /// <para><strong>Avoid When:</strong></para>
    /// <list type="bullet">
    /// <item><description>Loading large datasets with multiple navigation properties</description></item>
    /// <item><description>Performance is more critical than perfect consistency</description></item>
    /// <item><description>Working with read-only scenarios where slight inconsistency is acceptable</description></item>
    /// </list>
    /// </summary>
    /// <typeparam name="T">The entity type being queried</typeparam>
    /// <param name="query">The queryable to force into single query mode</param>
    /// <returns>A queryable configured to use single query execution</returns>
    public static IQueryable<T> RequireConsistency<T>(this IQueryable<T> query) where T : class => query.AsSingleQuery();
}
