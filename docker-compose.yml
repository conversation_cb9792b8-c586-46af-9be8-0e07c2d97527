version: '3.8'

services:
  postgres:
    image: postgres
    restart: always
    # set shared memory limit when using docker compose
    shm_size: 128mb
    # or set shared memory limit when deploy via swarm stack
    #volumes:
    #  - type: tmpfs
    #    target: /dev/shm
    #    tmpfs:
    #      size: 134217728 # 128*2^20 bytes = 128Mb
    volumes:
      - type: volume
        source: postgres-data
        target: /var/lib/postgresql/data
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres

  pgadmin:
    image: dpage/pgadmin4
    restart: always
    ports:
      - 5050:80
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
    volumes:
      - pgadmin-data:/var/lib/pgadmin
  
  rabbitmq:
    image: rabbitmq:latest
    restart: always
    ports:
      - "15672:15672"
      - "5672:5672"
    healthcheck:
      test: rabbitmq-diagnostics -q ping
      interval: 30s
      timeout: 30s
      retries: 3

  covergo-mongo:
    image: mongo:4.4.18
    restart: always
    environment:
      - MONG<PERSON>_INITDB_ROOT_USERNAME=root
      - MONGO_INITDB_ROOT_PASSWORD=local_dev
      - MONGO_INITDB_DATABASE=policies
    ports:
      - "27017:27017"

  redis:
    image: redis:latest
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis-data

  covergo-gateway:
    image: ghcr.io/covergo/gateway:latest
    environment:
      - ASPNETCORE_ENVIRONMENT=Staging
      - datacenterId=covergo-dockerComposeOnJenkins-hk
      - REDIS_CONNECTION_STRING=redis:6379
    ports:
      - "60060:8080" # To access localhost:60060/graphql
    depends_on:
      covergo-policies:
        condition: service_started
      covergo-policies-health:
        condition: service_started
      covergo-auth:
        condition: service_started
      covergo-auth-health:
        condition: service_healthy
      covergo-users:
        condition: service_started
      covergo-users-health:
        condition: service_healthy
      covergo-products:
        condition: service_started
      covergo-products-health:
        condition: service_healthy
      covergo-cases:
        condition: service_started
      covergo-cases-health:
        condition: service_healthy
      covergo-filesystem:
        condition: service_started

  covergo-gateway-health:
    image: busybox
    #use trap to exit gracefully from sleep
    command:
      ["/bin/sh", "-c", 'trap "echo exiting; exit 0" TERM; sleep 1d & wait']
    healthcheck:
      test: "wget http://covergo-gateway:8080/healthz -q -O -  || exit 1"
      interval: 1s
      timeout: 60s
      retries: 60
    depends_on:
      - covergo-gateway

  covergo-auth:
    image: ghcr.io/covergo/auth:master
    environment:
      - ASPNETCORE_ENVIRONMENT=Staging
      - isDeployment=true
      - datacenterId=developer-machine
      - DBCONFIG-providerId=mongoDb
      - DATABASE_CONNECT_STRING=**************************************
      - OTP_LOGIN_CIPHER_KEY=zaWgPou46nNmfMrYivTS2waJO9VKI277iLlPkGL56yc=
      - OTP_LOGIN_CIPHER_IV=94jCf53NO1acZ3pO7UE+gA==
      - OTP_LOGIN_HASHER_KEY=key
      - COVERGO_PASSWORD=V9K&KobcZO3
    ports:
      - "8080"
    depends_on:
      - covergo-mongo

  covergo-auth-health:
    image: busybox
    #use trap to exit gracefully from sleep
    command:
      ["/bin/sh", "-c", 'trap "echo exiting; exit 0" TERM; sleep 1d & wait']
    healthcheck:
      test: "wget http://covergo-auth:8080/healthz -q -O -  || exit 1"
      interval: 1s
      timeout: 1s
      retries: 60
    depends_on:
      - covergo-auth

  covergo-users:
    image: ghcr.io/covergo/users:latest
    environment:
      - ASPNETCORE_ENVIRONMENT=Staging
      - isDeployment=true
      - datacenterId=developer-machine
      - DATABASE_DRIVER=mongoDb
      - DATABASE_CONNECT_STRING=**************************************
    ports:
      - "8080"
    depends_on:
      - covergo-mongo

  covergo-users-health:
    image: busybox
    #use trap to exit gracefully from sleep
    command:
      ["/bin/sh", "-c", 'trap "echo exiting; exit 0" TERM; sleep 1d & wait']
    healthcheck:
      test: "wget http://covergo-users:8080/healthz -q -O -  || exit 1"
      interval: 1s
      timeout: 1s
      retries: 30
    depends_on:
      - covergo-users

  covergo-cases:
    image: ghcr.io/covergo/cases:latest
    environment:
      - ASPNETCORE_ENVIRONMENT=Staging
      - datacenterId=covergo-dockerComposeOnJenkins-hk
      - DATABASE_DRIVER=mongoDb
      - DBCONFIG-providerId=mongoDb
      - DBCONFIG-endpoint=covergo-mongo
      - DBCONFIG-username=root
      - DBCONFIG-password=local_dev
    ports:
      - "8080"
    depends_on:
      - covergo-mongo

  covergo-cases-health:
    image: busybox
    #use trap to exit gracefully from sleep
    command:
      ["/bin/sh", "-c", 'trap "echo exiting; exit 0" TERM; sleep 1d & wait']
    healthcheck:
      test: "wget http://covergo-cases:8080/healthz -q -O -  || exit 1"
      interval: 1s
      timeout: 1s
      retries: 30
    depends_on:
      - covergo-cases

  covergo-policies:
    image: ghcr.io/covergo/policies:latest
    restart: always
    environment:
      - ASPNETCORE_ENVIRONMENT=Staging.CI
      - isDeployment=true
      - datacenterId=developer-machine
      - DATABASE_DRIVER=mongoDb
      - DATABASE_CONNECT_STRING=**************************************
      - FeatureManagement__DisallowIndividualPolicyExtraFields__EnabledFor__0__Name=Tenants
      - FeatureManagement__DisallowIndividualPolicyExtraFields__EnabledFor__0__Parameters__Tenants__0=covergo
    ports:
      - 8080:8080
    depends_on:
      covergo-mongo:
        condition: service_started
      rabbitmq:
        condition: service_healthy
  
  covergo-policies-health:
    image: busybox
    #use trap to exit gracefully from sleep
    command:
      ["/bin/sh", "-c", 'trap "echo exiting; exit 0" TERM; sleep 1d & wait']
    healthcheck:
      test: "wget http://covergo-policies:8080/healthz -q -O -  || exit 1"
      interval: 1s
      timeout: 1s
      retries: 30
    depends_on:
      - covergo-policies

  covergo-filesystem:
    image: ghcr.io/covergo/filesystem:master
    environment:
      - ASPNETCORE_ENVIRONMENT=Staging
      - isDeployment=true
      - datacenterId=developer-machine
      - DATABASE_DRIVER=mongoDb
      - DATABASE_CONNECT_STRING=**************************************
      - MAX_REQUEST_BODY_SIZE=52428800
      - IS_USING_MINIO=true
      - MINIO_ENDPOINT=minio:9000
      - MINIO_ACCESS_KEY=AKIAIOSFODNN7EXAMPLE
      - MINIO_SECRET_KEY=wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY
      - MINIO_BUCKET_NAME=coverhealth--dev3
    ports:
      - "8080"
    depends_on:
      - covergo-mongo
      - minio
      - minio-create-bucket

  covergo-products:
    image: ghcr.io/covergo/products:latest
    environment:
      - ASPNETCORE_ENVIRONMENT=Staging
      - datacenterId=covergo-dockerComposeOnJenkins-hk
      - DATABASE_CONNECT_STRING=**************************************
    ports:
      - "8080"
    depends_on:
      - covergo-mongo

  covergo-products-health:
    image: busybox
    #use trap to exit gracefully from sleep
    command:
      ["/bin/sh", "-c", 'trap "echo exiting; exit 0" TERM; sleep 1d & wait']
    healthcheck:
      test: "wget http://covergo-products:8080/healthz -q -O -  || exit 1"
      interval: 1s
      timeout: 1s
      retries: 30
    depends_on:
      - covergo-products

  minio:
    image: minio/minio
    command: server -C /etc/minio --address ":9000" --console-address ":9001" /data
    ports:
      - "127.0.0.1:9000:9000"
      - "9001:9001"
    environment:
      - "MINIO_ACCESS_KEY=AKIAIOSFODNN7EXAMPLE"
      - "MINIO_SECRET_KEY=wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY"

      #MINIO_ROOT_USER: minioadmin
      #MINIO_ROOT_PASSWORD: minioadmin
    volumes:
      - minio-data
      - /etc/minio:/root/.minio/
      - /etc/minio:/etc/minio/

  minio-create-bucket:
    image: minio/mc
    depends_on:
      - minio      
    entrypoint: >
      /bin/sh -c "
      /usr/bin/mc config host add myminio http://minio:9000 AKIAIOSFODNN7EXAMPLE wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY;
      /usr/bin/mc rb myminio/coverhealth--dev3;
      /usr/bin/mc mb myminio/coverhealth--dev3 --region=MINIODEV;
      exit 0;
      "

  otel-collector:
    image: otel/opentelemetry-collector
    command: [--config=/etc/otel-collector-config.yaml]
    volumes:
      - ./otel-collector-config.yaml:/etc/otel-collector-config.yaml
    ports:
      - 4317:4317 # OTLP gRPC receiver
      - 4318:4318 # OTLP http receiver
      - 55679:55679 # zpages extension
    depends_on:
      - zipkin

  # Zipkin
  zipkin:
    image: openzipkin/zipkin:latest
    restart: always
    ports:
      - "9411:9411"                
        
  covergo-policies-v3:
    environment:
      - ObservabilityConfiguration__CollectorUrl=http://otel-collector:4317
      - ConnectionStrings__DefaultConnection=Host=postgres:5432;Database={tenant}_policies;Username=postgres;Password=postgres
    image: ghcr.io/covergo/policies-v3:latest
    ports:
      - "8080:8080"
    build:
      context: .
      dockerfile: ./Dockerfile     
  
  covergo-policies-v3-tests:
    environment:
      - ObservabilityConfiguration__CollectorUrl=http://otel-collector:4317
    image: ghcr.io/covergo/policies-v3-test:latest
    ports:
      - "8080:8080"
    build:
      context: .
      dockerfile: ./Dockerfile
      target: tests
    depends_on:
      rabbitmq:
        condition: service_healthy
      covergo-gateway-health:
        condition: service_healthy
      covergo-filesystem:
        condition: service_started
      covergo-policies-health:
        condition: service_healthy
      covergo-users-health:
        condition: service_healthy
      covergo-auth-health:
        condition: service_healthy
      covergo-products-health:
        condition: service_healthy
      covergo-cases-health:
        condition: service_healthy
      
volumes:
  minio-data:
  redis-data:
  postgres-data:
  pgadmin-data: